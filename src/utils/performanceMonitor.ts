/**
 * 性能监控和内存管理优化系统
 * 提供全面的性能监控、内存管理和性能优化功能
 */

import { KlineLogger } from '~/config/kline.config'

const logger = KlineLogger.getInstance()

// 性能指标接口
interface PerformanceMetrics {
  memoryUsage: {
    used: number
    total: number
    percentage: number
    heapUsed?: number
    heapTotal?: number
  }
  renderingPerformance: {
    fps: number
    frameDrops: number
    averageFrameTime: number
    lastFrameTime: number
  }
  dataFlowPerformance: {
    avgProcessingTime: number
    maxProcessingTime: number
    minProcessingTime: number
    totalOperations: number
    operationsPerSecond: number
  }
  networkPerformance: {
    avgLatency: number
    maxLatency: number
    minLatency: number
    totalRequests: number
    failedRequests: number
    successRate: number
  }
  cachePerformance: {
    hitRate: number
    missRate: number
    averageAccessTime: number
    totalSize: number
    evictionCount: number
  }
  componentPerformance: {
    mountTime: number
    updateTime: number
    unmountTime: number
    reRenderCount: number
    lastRenderTime: number
  }
}

// 性能警告阈值
interface PerformanceThresholds {
  memoryUsage: number
  maxFrameTime: number
  maxProcessingTime: number
  maxLatency: number
  minCacheHitRate: number
  maxRenderTime: number
}

// 内存管理配置
interface MemoryConfig {
  maxCacheSize: number
  gcInterval: number
  memoryWarningThreshold: number
  forcedCleanupThreshold: number
  enableMemoryOptimization: boolean
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics
  private thresholds: PerformanceThresholds
  private memoryConfig: MemoryConfig
  private lastFrameTime: number = 0
  private frameCount: number = 0
  private processingTimes: number[] = []
  private latencyHistory: number[] = []
  private observers: Map<string, Function[]> = new Map()
  private intervalId: number | null = null
  private memoryCleanupId: number | null = null
  private rafId: number | null = null

  constructor(
    thresholds: Partial<PerformanceThresholds> = {},
    memoryConfig: Partial<MemoryConfig> = {}
  ) {
    this.thresholds = {
      memoryUsage: 85, // 85%
      maxFrameTime: 16.67, // 60fps
      maxProcessingTime: 100, // 100ms
      maxLatency: 1000, // 1s
      minCacheHitRate: 80, // 80%
      maxRenderTime: 50, // 50ms
      ...thresholds
    }

    this.memoryConfig = {
      maxCacheSize: 100 * 1024 * 1024, // 100MB
      gcInterval: 300000, // 5分钟
      memoryWarningThreshold: 80, // 80%
      forcedCleanupThreshold: 90, // 90%
      enableMemoryOptimization: true,
      ...memoryConfig
    }

    this.metrics = this.initializeMetrics()
    this.startMonitoring()
    
    logger.info('性能监控系统已启动', {
      thresholds: this.thresholds,
      memoryConfig: this.memoryConfig
    })
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      memoryUsage: {
        used: 0,
        total: 0,
        percentage: 0
      },
      renderingPerformance: {
        fps: 0,
        frameDrops: 0,
        averageFrameTime: 0,
        lastFrameTime: 0
      },
      dataFlowPerformance: {
        avgProcessingTime: 0,
        maxProcessingTime: 0,
        minProcessingTime: Infinity,
        totalOperations: 0,
        operationsPerSecond: 0
      },
      networkPerformance: {
        avgLatency: 0,
        maxLatency: 0,
        minLatency: Infinity,
        totalRequests: 0,
        failedRequests: 0,
        successRate: 100
      },
      cachePerformance: {
        hitRate: 0,
        missRate: 0,
        averageAccessTime: 0,
        totalSize: 0,
        evictionCount: 0
      },
      componentPerformance: {
        mountTime: 0,
        updateTime: 0,
        unmountTime: 0,
        reRenderCount: 0,
        lastRenderTime: 0
      }
    }
  }

  // 启动监控
  private startMonitoring() {
    if (typeof window === 'undefined') return

    // 内存监控
    this.intervalId = window.setInterval(() => {
      this.updateMemoryMetrics()
      this.checkPerformanceThresholds()
    }, 5000) // 每5秒检查一次

    // 内存清理
    if (this.memoryConfig.enableMemoryOptimization) {
      this.memoryCleanupId = window.setInterval(() => {
        this.performMemoryCleanup()
      }, this.memoryConfig.gcInterval)
    }

    // FPS监控
    this.startFPSMonitoring()
  }

  // FPS监控
  private startFPSMonitoring() {
    const measureFPS = (timestamp: number) => {
      if (this.lastFrameTime) {
        const frameTime = timestamp - this.lastFrameTime
        this.metrics.renderingPerformance.lastFrameTime = frameTime
        
        // 计算FPS
        this.frameCount++
        if (this.frameCount >= 60) { // 每60帧计算一次平均值
          const avgFrameTime = frameTime / this.frameCount
          this.metrics.renderingPerformance.averageFrameTime = avgFrameTime
          this.metrics.renderingPerformance.fps = 1000 / avgFrameTime
          
          // 检测掉帧
          if (frameTime > this.thresholds.maxFrameTime) {
            this.metrics.renderingPerformance.frameDrops++
          }
          
          this.frameCount = 0
        }
      }
      
      this.lastFrameTime = timestamp
      this.rafId = requestAnimationFrame(measureFPS)
    }

    this.rafId = requestAnimationFrame(measureFPS)
  }

  // 更新内存指标
  private updateMemoryMetrics() {
    if (typeof window === 'undefined' || !('performance' in window)) return

    const performance = window.performance as any

    // 尝试获取内存信息
    if (performance.memory) {
      const memory = performance.memory
      this.metrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
        heapUsed: memory.usedJSHeapSize,
        heapTotal: memory.totalJSHeapSize
      }
    } else {
      // 估算内存使用
      const estimatedUsage = this.estimateMemoryUsage()
      this.metrics.memoryUsage = {
        used: estimatedUsage.used,
        total: estimatedUsage.total,
        percentage: estimatedUsage.percentage
      }
    }
  }

  // 估算内存使用（当无法获取精确数据时）
  private estimateMemoryUsage(): { used: number, total: number, percentage: number } {
    // 基于已知的缓存和对象大小进行估算
    let estimatedUsed = 0
    
    // 估算DOM节点占用
    const domNodes = document.querySelectorAll('*').length
    estimatedUsed += domNodes * 100 // 假设每个DOM节点100字节

    // 估算事件监听器占用
    estimatedUsed += domNodes * 50 // 假设每个节点平均50字节的事件监听器

    const estimatedTotal = 128 * 1024 * 1024 // 假设总内存128MB

    return {
      used: estimatedUsed,
      total: estimatedTotal,
      percentage: (estimatedUsed / estimatedTotal) * 100
    }
  }

  // 检查性能阈值
  private checkPerformanceThresholds() {
    const issues: string[] = []

    // 内存检查
    if (this.metrics.memoryUsage.percentage > this.thresholds.memoryUsage) {
      issues.push(`内存使用率过高: ${this.metrics.memoryUsage.percentage.toFixed(2)}%`)
    }

    // 渲染性能检查
    if (this.metrics.renderingPerformance.averageFrameTime > this.thresholds.maxFrameTime) {
      issues.push(`帧率过低: ${this.metrics.renderingPerformance.fps.toFixed(2)} FPS`)
    }

    // 数据处理性能检查
    if (this.metrics.dataFlowPerformance.avgProcessingTime > this.thresholds.maxProcessingTime) {
      issues.push(`数据处理时间过长: ${this.metrics.dataFlowPerformance.avgProcessingTime.toFixed(2)}ms`)
    }

    // 网络性能检查
    if (this.metrics.networkPerformance.avgLatency > this.thresholds.maxLatency) {
      issues.push(`网络延迟过高: ${this.metrics.networkPerformance.avgLatency.toFixed(2)}ms`)
    }

    // 缓存性能检查
    if (this.metrics.cachePerformance.hitRate < this.thresholds.minCacheHitRate) {
      issues.push(`缓存命中率过低: ${this.metrics.cachePerformance.hitRate.toFixed(2)}%`)
    }

    if (issues.length > 0) {
      logger.warn('性能警告', { issues, currentMetrics: this.metrics })
      this.notifyObservers('performance_warning', { issues, metrics: this.metrics })
    }
  }

  // 执行内存清理
  private performMemoryCleanup() {
    logger.debug('开始内存清理')

    // 强制垃圾回收（如果支持）
    if (typeof window !== 'undefined' && (window as any).gc) {
      try {
        (window as any).gc()
        logger.debug('执行了强制垃圾回收')
      } catch (error) {
        logger.warn('强制垃圾回收失败', error)
      }
    }

    // 清理大型数组和对象
    this.cleanupProcessingHistory()

    // 通知缓存清理
    this.notifyObservers('memory_cleanup', { 
      memoryUsage: this.metrics.memoryUsage 
    })

    logger.debug('内存清理完成')
  }

  // 清理处理历史记录
  private cleanupProcessingHistory() {
    const maxHistorySize = 1000

    if (this.processingTimes.length > maxHistorySize) {
      this.processingTimes = this.processingTimes.slice(-maxHistorySize / 2)
    }

    if (this.latencyHistory.length > maxHistorySize) {
      this.latencyHistory = this.latencyHistory.slice(-maxHistorySize / 2)
    }
  }

  // 记录数据处理性能
  public recordDataProcessing(startTime: number, endTime: number = Date.now()) {
    const processingTime = endTime - startTime
    this.processingTimes.push(processingTime)

    // 更新指标
    this.metrics.dataFlowPerformance.totalOperations++
    this.metrics.dataFlowPerformance.avgProcessingTime = 
      this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length
    this.metrics.dataFlowPerformance.maxProcessingTime = 
      Math.max(this.metrics.dataFlowPerformance.maxProcessingTime, processingTime)
    this.metrics.dataFlowPerformance.minProcessingTime = 
      Math.min(this.metrics.dataFlowPerformance.minProcessingTime, processingTime)

    // 计算每秒操作数
    const now = Date.now()
    const recentOperations = this.processingTimes.filter(time => 
      (now - time) < 1000
    ).length
    this.metrics.dataFlowPerformance.operationsPerSecond = recentOperations

    logger.debug('数据处理性能记录', {
      processingTime,
      avgTime: this.metrics.dataFlowPerformance.avgProcessingTime,
      opsPerSecond: this.metrics.dataFlowPerformance.operationsPerSecond
    })
  }

  // 记录网络请求性能
  public recordNetworkRequest(latency: number, success: boolean = true) {
    this.latencyHistory.push(latency)
    this.metrics.networkPerformance.totalRequests++
    
    if (!success) {
      this.metrics.networkPerformance.failedRequests++
    }

    // 更新指标
    this.metrics.networkPerformance.avgLatency = 
      this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length
    this.metrics.networkPerformance.maxLatency = 
      Math.max(this.metrics.networkPerformance.maxLatency, latency)
    this.metrics.networkPerformance.minLatency = 
      Math.min(this.metrics.networkPerformance.minLatency, latency)
    this.metrics.networkPerformance.successRate = 
      ((this.metrics.networkPerformance.totalRequests - this.metrics.networkPerformance.failedRequests) / 
       this.metrics.networkPerformance.totalRequests) * 100

    logger.debug('网络请求性能记录', {
      latency,
      success,
      avgLatency: this.metrics.networkPerformance.avgLatency,
      successRate: this.metrics.networkPerformance.successRate
    })
  }

  // 记录缓存性能
  public recordCacheAccess(hit: boolean, accessTime: number = 0) {
    if (hit) {
      this.metrics.cachePerformance.hitRate++
    } else {
      this.metrics.cachePerformance.missRate++
    }

    const total = this.metrics.cachePerformance.hitRate + this.metrics.cachePerformance.missRate
    const hitRate = (this.metrics.cachePerformance.hitRate / total) * 100
    this.metrics.cachePerformance.hitRate = hitRate

    if (accessTime > 0) {
      // 更新平均访问时间（简化计算）
      this.metrics.cachePerformance.averageAccessTime = 
        (this.metrics.cachePerformance.averageAccessTime + accessTime) / 2
    }
  }

  // 记录组件性能
  public recordComponentPerformance(type: 'mount' | 'update' | 'unmount', time: number) {
    this.metrics.componentPerformance.reRenderCount++
    this.metrics.componentPerformance.lastRenderTime = time

    switch (type) {
      case 'mount':
        this.metrics.componentPerformance.mountTime = time
        break
      case 'update':
        this.metrics.componentPerformance.updateTime = time
        break
      case 'unmount':
        this.metrics.componentPerformance.unmountTime = time
        break
    }

    if (time > this.thresholds.maxRenderTime) {
      logger.warn('组件渲染时间过长', { type, time, threshold: this.thresholds.maxRenderTime })
    }
  }

  // 添加性能观察者
  public addObserver(event: string, callback: Function) {
    if (!this.observers.has(event)) {
      this.observers.set(event, [])
    }
    this.observers.get(event)!.push(callback)
  }

  // 移除性能观察者
  public removeObserver(event: string, callback: Function) {
    const callbacks = this.observers.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 通知观察者
  private notifyObservers(event: string, data: any) {
    const callbacks = this.observers.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          logger.error('性能观察者回调失败', { event, error })
        }
      })
    }
  }

  // 获取当前性能指标
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  // 获取性能报告
  public getPerformanceReport(): any {
    return {
      timestamp: Date.now(),
      metrics: this.getMetrics(),
      thresholds: this.thresholds,
      memoryConfig: this.memoryConfig,
      recommendations: this.generateRecommendations()
    }
  }

  // 生成性能优化建议
  private generateRecommendations(): string[] {
    const recommendations: string[] = []

    if (this.metrics.memoryUsage.percentage > 80) {
      recommendations.push('考虑增加内存清理频率或减少缓存大小')
    }

    if (this.metrics.renderingPerformance.fps < 30) {
      recommendations.push('优化组件渲染逻辑，减少不必要的重渲染')
    }

    if (this.metrics.dataFlowPerformance.avgProcessingTime > 50) {
      recommendations.push('优化数据处理算法，考虑使用Worker处理大量数据')
    }

    if (this.metrics.networkPerformance.successRate < 95) {
      recommendations.push('检查网络连接稳定性，考虑增加重试机制')
    }

    if (this.metrics.cachePerformance.hitRate < 70) {
      recommendations.push('调整缓存策略，增加缓存时间或改善缓存算法')
    }

    return recommendations
  }

  // 重置指标
  public resetMetrics() {
    this.metrics = this.initializeMetrics()
    this.processingTimes = []
    this.latencyHistory = []
    this.frameCount = 0
    logger.info('性能指标已重置')
  }

  // 停止监控
  public stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    if (this.memoryCleanupId) {
      clearInterval(this.memoryCleanupId)
      this.memoryCleanupId = null
    }

    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
      this.rafId = null
    }

    this.observers.clear()
    logger.info('性能监控已停止')
  }
}

// 创建全局性能监控实例
export const createPerformanceMonitor = (
  thresholds?: Partial<PerformanceThresholds>,
  memoryConfig?: Partial<MemoryConfig>
) => {
  return new PerformanceMonitor(thresholds, memoryConfig)
}

// 性能装饰器
export function performanceTrack(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = function(...args: any[]) {
    const startTime = Date.now()
    const result = originalMethod.apply(this, args)
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const endTime = Date.now()
        logger.debug(`方法 ${propertyKey} 执行时间`, { 
          duration: endTime - startTime,
          args: args.length 
        })
      })
    } else {
      const endTime = Date.now()
      logger.debug(`方法 ${propertyKey} 执行时间`, { 
        duration: endTime - startTime,
        args: args.length 
      })
      return result
    }
  }

  return descriptor
}

export default PerformanceMonitor