/**
 * K线数据处理器 - 实现数据无缝衔接的核心工具类
 * 提供历史数据与实时数据的智能合并、验证和处理功能
 */

import { KLINE_CONFIG, KlineLogger, type KlineBarData } from '~/config/kline.config'

const logger = KlineLogger.getInstance()

/**
 * 时间边界检测结果
 */
interface TimeBoundaryResult {
  isNewBar: boolean        // 是否需要创建新的K线
  shouldUpdate: boolean    // 是否应该更新现有K线
  currentBarTime: number   // 当前K线时间
  reason: string          // 判断原因
}

/**
 * 数据合并配置
 */
interface DataMergeOptions {
  preserveVolume?: boolean     // 是否保留成交量
  enablePriceValidation?: boolean  // 是否启用价格验证
  maxPriceJumpPercent?: number    // 最大价格跳跃百分比
  enableTimeValidation?: boolean   // 是否启用时间验证
}

/**
 * 数据间隙信息
 */
interface DataGap {
  startTime: number
  endTime: number
  gapMinutes: number
  severity: 'minor' | 'major' | 'critical'
}

/**
 * K线数据处理器类
 */
export class KlineDataProcessor {
  private resolution: string
  private logger = KlineLogger.getInstance()
  
  constructor(resolution: string) {
    this.resolution = resolution
  }

  /**
   * 获取当前K线周期的开始时间
   */
  getCurrentBarStartTime(currentTime: number = Date.now()): number {
    const resolutionMinutes: Record<string, number> = {
      '1m': 1, '5m': 5, '15m': 15, '30m': 30, '1h': 60,
      '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720
    }
    
    if (this.resolution === '1d') {
      const date = new Date(currentTime)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    } else if (this.resolution === '1w') {
      const date = new Date(currentTime)
      const day = date.getUTCDay()
      date.setUTCDate(date.getUTCDate() - day)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    } else if (this.resolution === '1M') {
      const date = new Date(currentTime)
      date.setUTCDate(1)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    }
    
    const minutes = resolutionMinutes[this.resolution] || 60
    const msPerMinute = 60 * 1000
    const periodMs = minutes * msPerMinute
    return Math.floor(currentTime / periodMs) * periodMs
  }

  /**
   * 智能时间边界检测
   */
  detectTimeBoundary(
    newDataTime: number, 
    lastBarTime: number | null, 
    currentTime: number = Date.now()
  ): TimeBoundaryResult {
    const currentBarTime = this.getCurrentBarStartTime(currentTime)
    const newBarTime = this.getCurrentBarStartTime(newDataTime)
    
    // 如果没有历史数据，创建新K线
    if (!lastBarTime) {
      return {
        isNewBar: true,
        shouldUpdate: false,
        currentBarTime: newBarTime,
        reason: 'No previous bar data'
      }
    }
    
    // 如果新数据的时间等于当前K线时间，更新现有K线
    if (newBarTime === this.getCurrentBarStartTime(lastBarTime)) {
      return {
        isNewBar: false,
        shouldUpdate: true,
        currentBarTime: newBarTime,
        reason: 'Update current bar'
      }
    }
    
    // 如果新数据的时间大于最后一个K线时间，创建新K线
    if (newBarTime > lastBarTime) {
      return {
        isNewBar: true,
        shouldUpdate: false,
        currentBarTime: newBarTime,
        reason: 'New time period'
      }
    }
    
    // 如果新数据时间小于最后一个K线时间，可能是历史数据
    return {
      isNewBar: false,
      shouldUpdate: false,
      currentBarTime: newBarTime,
      reason: 'Historical data or time conflict'
    }
  }

  /**
   * 智能数据合并
   */
  mergeKlineData(
    historicalData: KlineBarData[],
    realtimeData: Partial<KlineBarData>,
    options: DataMergeOptions = {}
  ): KlineBarData[] {
    const {
      preserveVolume = true,
      enablePriceValidation = true,
      maxPriceJumpPercent = 50,
      enableTimeValidation = true
    } = options

    if (!historicalData || historicalData.length === 0) {
      logger.warn('没有历史数据进行合并')
      return []
    }

    const result = [...historicalData]
    const lastBar = result[result.length - 1]
    
    if (!realtimeData || !realtimeData.time) {
      logger.warn('实时数据无效，使用历史数据')
      return result
    }

    const timeBoundary = this.detectTimeBoundary(
      realtimeData.time,
      lastBar.time,
      Date.now()
    )

    logger.debug('时间边界检测结果', {
      isNewBar: timeBoundary.isNewBar,
      shouldUpdate: timeBoundary.shouldUpdate,
      reason: timeBoundary.reason
    })

    if (timeBoundary.isNewBar) {
      // 创建新的K线
      const newBar: KlineBarData = {
        time: timeBoundary.currentBarTime,
        open: realtimeData.close || lastBar.close,
        high: realtimeData.close || lastBar.close,
        low: realtimeData.close || lastBar.close,
        close: realtimeData.close || lastBar.close,
        volume: preserveVolume ? (realtimeData.volume || 0) : 0
      }
      
      result.push(newBar)
      logger.debug('创建新K线', newBar)
      
    } else if (timeBoundary.shouldUpdate) {
      // 更新现有K线
      const updatedBar: KlineBarData = {
        ...lastBar,
        high: Math.max(lastBar.high, realtimeData.close || lastBar.close),
        low: Math.min(lastBar.low, realtimeData.close || lastBar.close),
        close: realtimeData.close || lastBar.close,
        volume: preserveVolume ? (realtimeData.volume || lastBar.volume) : lastBar.volume
      }

      // 价格验证
      if (enablePriceValidation && realtimeData.close) {
        const priceChange = Math.abs(realtimeData.close - lastBar.close) / lastBar.close * 100
        if (priceChange > maxPriceJumpPercent) {
          logger.warn('检测到异常价格跳跃', {
            oldPrice: lastBar.close,
            newPrice: realtimeData.close,
            changePercent: priceChange
          })
        }
      }

      result[result.length - 1] = updatedBar
      logger.debug('更新现有K线', updatedBar)
    }

    return result
  }

  /**
   * 检测数据间隙
   */
  detectDataGaps(data: KlineBarData[]): DataGap[] {
    if (!data || data.length < 2) {
      return []
    }

    const gaps: DataGap[] = []
    const expectedInterval = this.getIntervalMs()
    const maxAllowedGap = expectedInterval * 2 // 允许的最大间隙

    for (let i = 1; i < data.length; i++) {
      const prevBar = data[i - 1]
      const currentBar = data[i]
      const actualGap = currentBar.time - prevBar.time

      if (actualGap > maxAllowedGap) {
        const gapMinutes = Math.round(actualGap / 60000)
        let severity: DataGap['severity'] = 'minor'
        
        if (gapMinutes > 60) severity = 'major'
        if (gapMinutes > 240) severity = 'critical'

        gaps.push({
          startTime: prevBar.time,
          endTime: currentBar.time,
          gapMinutes,
          severity
        })
      }
    }

    if (gaps.length > 0) {
      logger.info('检测到数据间隙', { gaps: gaps.length, details: gaps })
    }

    return gaps
  }

  /**
   * 填充数据间隙
   */
  fillDataGaps(data: KlineBarData[]): KlineBarData[] {
    const gaps = this.detectDataGaps(data)
    
    if (gaps.length === 0) {
      return data
    }

    const result: KlineBarData[] = []
    const intervalMs = this.getIntervalMs()

    for (let i = 0; i < data.length; i++) {
      result.push(data[i])

      // 检查是否需要填充间隙
      if (i < data.length - 1) {
        const currentBar = data[i]
        const nextBar = data[i + 1]
        const gap = nextBar.time - currentBar.time

        if (gap > intervalMs * 2) {
          const gapsToFill = Math.floor(gap / intervalMs) - 1
          
          for (let j = 1; j <= gapsToFill; j++) {
            const fillTime = currentBar.time + (j * intervalMs)
            const fillBar: KlineBarData = {
              time: fillTime,
              open: currentBar.close,
              high: currentBar.close,
              low: currentBar.close,
              close: currentBar.close,
              volume: 0
            }
            result.push(fillBar)
          }
        }
      }
    }

    const filledCount = result.length - data.length
    if (filledCount > 0) {
      logger.info('数据间隙填充完成', {
        原始数据量: data.length,
        填充后数据量: result.length,
        填充数量: filledCount
      })
    }

    return result.sort((a, b) => a.time - b.time)
  }

  /**
   * 实时数据与历史数据无缝衔接
   */
  seamlessConnect(
    historicalData: KlineBarData[],
    realtimePrice: number,
    realtimeVolume?: number,
    realtimeTime?: number
  ): KlineBarData[] {
    const currentTime = realtimeTime || Date.now()
    
    logger.debug('开始无缝衔接处理', {
      历史数据量: historicalData.length,
      实时价格: realtimePrice,
      实时时间: new Date(currentTime).toISOString()
    })

    const realtimeData: Partial<KlineBarData> = {
      time: currentTime,
      close: realtimePrice,
      volume: realtimeVolume
    }

    return this.mergeKlineData(historicalData, realtimeData, {
      preserveVolume: true,
      enablePriceValidation: true,
      enableTimeValidation: true
    })
  }

  /**
   * 数据质量验证
   */
  validateDataQuality(data: KlineBarData[]): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    if (!data || data.length === 0) {
      errors.push('数据为空')
      return { isValid: false, errors, warnings }
    }

    // 验证数据格式
    for (let i = 0; i < data.length; i++) {
      const bar = data[i]
      
      if (!bar.time || typeof bar.time !== 'number') {
        errors.push(`第${i + 1}个K线时间戳无效`)
      }
      
      if (typeof bar.open !== 'number' || typeof bar.high !== 'number' ||
          typeof bar.low !== 'number' || typeof bar.close !== 'number') {
        errors.push(`第${i + 1}个K线价格数据无效`)
      }
      
      if (bar.high < Math.max(bar.open, bar.close)) {
        warnings.push(`第${i + 1}个K线最高价异常`)
      }
      
      if (bar.low > Math.min(bar.open, bar.close)) {
        warnings.push(`第${i + 1}个K线最低价异常`)
      }
    }

    // 验证时间顺序
    for (let i = 1; i < data.length; i++) {
      if (data[i].time <= data[i - 1].time) {
        warnings.push(`第${i + 1}个K线时间顺序异常`)
      }
    }

    const isValid = errors.length === 0
    
    logger.debug('数据质量验证完成', {
      数据量: data.length,
      错误数: errors.length,
      警告数: warnings.length,
      验证结果: isValid ? '通过' : '失败'
    })

    return { isValid, errors, warnings }
  }

  /**
   * 获取周期对应的毫秒数
   */
  private getIntervalMs(): number {
    const intervalMap: Record<string, number> = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '8h': 8 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000,
      '1M': 30 * 24 * 60 * 60 * 1000
    }
    return intervalMap[this.resolution] || 60 * 60 * 1000
  }

  /**
   * 数据标准化和清理
   */
  normalizeData(rawData: any[]): KlineBarData[] {
    if (!Array.isArray(rawData)) {
      logger.warn('原始数据格式无效')
      return []
    }

    const normalizedData: KlineBarData[] = []

    for (const item of rawData) {
      try {
        let klineData: KlineBarData

        if (Array.isArray(item)) {
          // 数组格式: [timestamp, open, high, low, close, volume]
          klineData = {
            time: Number(item[0]) || 0,
            open: Math.abs(Number(item[1]) || 0),
            high: Math.abs(Number(item[2]) || 0),
            low: Math.abs(Number(item[3]) || 0),
            close: Math.abs(Number(item[4]) || 0),
            volume: Math.abs(Number(item[5]) || 0)
          }
        } else if (typeof item === 'object') {
          // 对象格式
          klineData = {
            time: Number(item.time || item.timestamp) || 0,
            open: Math.abs(Number(item.open) || 0),
            high: Math.abs(Number(item.high) || 0),
            low: Math.abs(Number(item.low) || 0),
            close: Math.abs(Number(item.close) || 0),
            volume: Math.abs(Number(item.volume) || 0)
          }
        } else {
          logger.warn('跳过无效数据项', item)
          continue
        }

        // 基本验证
        if (klineData.time > 0 && klineData.close > 0) {
          normalizedData.push(klineData)
        }
      } catch (error) {
        logger.warn('数据标准化失败', { item, error })
      }
    }

    logger.debug('数据标准化完成', {
      原始数据量: rawData.length,
      有效数据量: normalizedData.length,
      过滤掉: rawData.length - normalizedData.length
    })

    return normalizedData.sort((a, b) => a.time - b.time)
  }
}

/**
 * 创建K线数据处理器实例
 */
export function createKlineProcessor(resolution: string): KlineDataProcessor {
  return new KlineDataProcessor(resolution)
}

/**
 * 工具函数：快速数据合并
 */
export function quickMergeKlineData(
  historicalData: KlineBarData[],
  realtimePrice: number,
  resolution: string,
  realtimeVolume?: number
): KlineBarData[] {
  const processor = createKlineProcessor(resolution)
  return processor.seamlessConnect(historicalData, realtimePrice, realtimeVolume)
}

/**
 * 工具函数：数据质量检查
 */
export function validateKlineData(data: KlineBarData[]): boolean {
  if (!data || data.length === 0) return false
  
  // 快速检查关键字段
  for (const bar of data) {
    if (!bar.time || !bar.close || typeof bar.time !== 'number' || typeof bar.close !== 'number') {
      return false
    }
  }
  
  return true
}

export default KlineDataProcessor