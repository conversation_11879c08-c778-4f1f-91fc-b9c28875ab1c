/**
 * 1w和1M修复效果测试助手
 * 用于验证修复后的行为是否正确
 */

import { KlineLogger } from '~/config/kline.config'

export class WeeklyMonthlyTestHelper {
  private logger = KlineLogger.getInstance()
  private requestCounts = new Map<string, number>()
  private timerCounts = new Map<string, number>()
  
  constructor() {
    this.setupRequestMonitoring()
    this.setupTimerMonitoring()
  }
  
  /**
   * 监控API请求次数
   */
  private setupRequestMonitoring() {
    // 监控fetch请求
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const url = args[0] as string
      if (url.includes('/api/v1/order/klines')) {
        const urlObj = new URL(url, window.location.origin)
        const timeFrame = urlObj.searchParams.get('time_frame')
        
        if (timeFrame === '1w' || timeFrame === '1M') {
          const key = `${timeFrame}-api-request`
          this.requestCounts.set(key, (this.requestCounts.get(key) || 0) + 1)
          
          this.logger.info(`${timeFrame}接口调用`, {
            调用次数: this.requestCounts.get(key),
            URL: url,
            时间: new Date().toISOString()
          })
        }
      }
      
      return originalFetch.apply(window, args)
    }
  }
  
  /**
   * 监控定时器使用
   */
  private setupTimerMonitoring() {
    const originalSetTimeout = window.setTimeout
    const originalSetInterval = window.setInterval
    
    window.setTimeout = (callback, delay, ...args) => {
      const stack = new Error().stack
      if (stack && (stack.includes('TradingView') || stack.includes('kline') || stack.includes('datafeed'))) {
        const key = 'setTimeout-kline'
        this.timerCounts.set(key, (this.timerCounts.get(key) || 0) + 1)
        
        this.logger.warn('检测到K线相关setTimeout', {
          延迟: delay,
          调用次数: this.timerCounts.get(key),
          堆栈: stack?.split('\n').slice(0, 3).join('\n')
        })
      }
      
      return originalSetTimeout.call(window, callback, delay, ...args)
    }
    
    window.setInterval = (callback, delay, ...args) => {
      const stack = new Error().stack
      if (stack && (stack.includes('TradingView') || stack.includes('kline') || stack.includes('datafeed'))) {
        const key = 'setInterval-kline'
        this.timerCounts.set(key, (this.timerCounts.get(key) || 0) + 1)
        
        this.logger.warn('检测到K线相关setInterval', {
          延迟: delay,
          调用次数: this.timerCounts.get(key),
          堆栈: stack?.split('\n').slice(0, 3).join('\n')
        })
      }
      
      return originalSetInterval.call(window, callback, delay, ...args)
    }
  }
  
  /**
   * 获取测试报告
   */
  getTestReport() {
    const report = {
      API请求统计: Object.fromEntries(this.requestCounts),
      定时器统计: Object.fromEntries(this.timerCounts),
      测试时间: new Date().toISOString(),
      修复状态: {
        '1w接口调用次数': this.requestCounts.get('1w-api-request') || 0,
        '1M接口调用次数': this.requestCounts.get('1M-api-request') || 0,
        '定时器使用次数': Array.from(this.timerCounts.values()).reduce((sum, count) => sum + count, 0)
      }
    }
    
    // 判断修复是否成功
    const is1wFixed = (this.requestCounts.get('1w-api-request') || 0) <= 1
    const is1MFixed = (this.requestCounts.get('1M-api-request') || 0) <= 1
    const isTimerFixed = Array.from(this.timerCounts.values()).reduce((sum, count) => sum + count, 0) === 0
    
    report.修复状态['修复成功'] = is1wFixed && is1MFixed && isTimerFixed
    
    this.logger.info('1w和1M修复效果测试报告', report)
    
    return report
  }
  
  /**
   * 重置统计
   */
  reset() {
    this.requestCounts.clear()
    this.timerCounts.clear()
    this.logger.info('测试统计已重置')
  }
  
  /**
   * 开始测试会话
   */
  startTestSession(resolution: '1w' | '1M') {
    this.reset()
    this.logger.info(`开始${resolution}测试会话`, {
      开始时间: new Date().toISOString(),
      分辨率: resolution
    })
  }
  
  /**
   * 结束测试会话
   */
  endTestSession(resolution: '1w' | '1M') {
    const report = this.getTestReport()
    
    this.logger.info(`${resolution}测试会话结束`, {
      结束时间: new Date().toISOString(),
      分辨率: resolution,
      测试结果: report.修复状态
    })
    
    return report
  }
}

// 全局测试助手实例
export const weeklyMonthlyTestHelper = new WeeklyMonthlyTestHelper()

// 在开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.weeklyMonthlyTestHelper = weeklyMonthlyTestHelper
  console.log('1w和1M测试助手已启用，使用 window.weeklyMonthlyTestHelper 访问')
}
