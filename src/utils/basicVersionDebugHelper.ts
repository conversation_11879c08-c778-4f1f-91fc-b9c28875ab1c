/**
 * 基本版调试助手 - 深度追踪API调用来源
 */

export class BasicVersionDebugHelper {
  private callStack = new Map<string, Array<{
    timestamp: number,
    source: string,
    resolution: string,
    stackTrace: string
  }>>()
  
  constructor() {
    this.setupDeepMonitoring()
  }
  
  /**
   * 设置深度监控
   */
  private setupDeepMonitoring() {
    // 监控API调用
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const url = args[0] as string
      if (url.includes('/api/v1/candles')) {
        const urlObj = new URL(url, window.location.origin)
        const timeFrame = urlObj.searchParams.get('time_frame')
        
        if (timeFrame) {
          const stackTrace = new Error().stack || ''
          const source = this.identifyCallSource(stackTrace)
          
          const key = `${timeFrame}-calls`
          if (!this.callStack.has(key)) {
            this.callStack.set(key, [])
          }
          
          this.callStack.get(key)!.push({
            timestamp: Date.now(),
            source,
            resolution: timeFrame,
            stackTrace: stackTrace.split('\n').slice(0, 10).join('\n')
          })
          
          console.group(`🔍 API调用追踪: ${timeFrame}`)
          console.log('调用来源:', source)
          console.log('URL:', url)
          console.log('调用次数:', this.callStack.get(key)!.length)
          console.log('堆栈追踪:', stackTrace.split('\n').slice(1, 5).join('\n'))
          console.groupEnd()
        }
      }
      
      return originalFetch.apply(window, args)
    }
    
    // 监控chart.applyNewData调用
    this.monitorChartMethods()
  }
  
  /**
   * 识别调用来源
   */
  private identifyCallSource(stackTrace: string): string {
    if (stackTrace.includes('setLoadMoreDataCallback')) {
      return 'LoadMore回调'
    } else if (stackTrace.includes('request') && stackTrace.includes('nextTick')) {
      return '组件挂载初始化'
    } else if (stackTrace.includes('chartInit')) {
      return 'chartInit初始化'
    } else if (stackTrace.includes('watch')) {
      return 'klineList监听器'
    } else if (stackTrace.includes('getKlineSocket')) {
      return 'WebSocket降级'
    } else {
      return '未知来源'
    }
  }
  
  /**
   * 监控图表方法
   */
  private monitorChartMethods() {
    // 代理console.log来监控图表相关日志
    const originalLog = console.log
    console.log = (...args) => {
      const message = args.join(' ')
      
      // 监控applyNewData调用
      if (message.includes('chartInit:') || message.includes('klineList watch:')) {
        const match = message.match(/(1d|1w|1M).*启用loadMore:\s*(true|false)/)
        if (match) {
          const resolution = match[1]
          const enableLoadMore = match[2] === 'true'
          
          console.warn(`📊 图表loadMore状态: ${resolution} = ${enableLoadMore}`, {
            消息: message,
            时间: new Date().toISOString()
          })
        }
      }
      
      return originalLog.apply(console, args)
    }
  }
  
  /**
   * 获取调用统计
   */
  getCallStatistics() {
    const stats = {}
    
    for (const [key, calls] of this.callStack.entries()) {
      const resolution = key.replace('-calls', '')
      stats[resolution] = {
        总调用次数: calls.length,
        调用来源统计: this.getSourceStatistics(calls),
        最近5次调用: calls.slice(-5).map(call => ({
          时间: new Date(call.timestamp).toLocaleTimeString(),
          来源: call.source
        }))
      }
    }
    
    return stats
  }
  
  /**
   * 获取来源统计
   */
  private getSourceStatistics(calls: Array<any>) {
    const sourceCount = {}
    calls.forEach(call => {
      sourceCount[call.source] = (sourceCount[call.source] || 0) + 1
    })
    return sourceCount
  }
  
  /**
   * 获取详细报告
   */
  getDetailedReport() {
    const report = {
      调用统计: this.getCallStatistics(),
      问题分析: this.analyzeProblems(),
      修复建议: this.getFixSuggestions(),
      生成时间: new Date().toISOString()
    }
    
    console.group('📋 基本版API调用详细报告')
    console.table(report.调用统计)
    console.log('问题分析:', report.问题分析)
    console.log('修复建议:', report.修复建议)
    console.groupEnd()
    
    return report
  }
  
  /**
   * 分析问题
   */
  private analyzeProblems() {
    const problems = []
    const stats = this.getCallStatistics()
    
    // 检查1d是否调用了API
    if (stats['1d'] && stats['1d'].总调用次数 > 0) {
      problems.push({
        问题: '1d分辨率调用了API',
        严重程度: '高',
        调用次数: stats['1d'].总调用次数,
        主要来源: Object.keys(stats['1d'].调用来源统计)[0]
      })
    }
    
    // 检查1w和1M是否调用过多
    ['1w', '1M'].forEach(resolution => {
      if (stats[resolution] && stats[resolution].总调用次数 > 3) {
        problems.push({
          问题: `${resolution}分辨率调用过多`,
          严重程度: '中',
          调用次数: stats[resolution].总调用次数,
          主要来源: Object.keys(stats[resolution].调用来源统计)[0]
        })
      }
    })
    
    return problems
  }
  
  /**
   * 获取修复建议
   */
  private getFixSuggestions() {
    const suggestions = []
    const stats = this.getCallStatistics()
    
    if (stats['1d']) {
      suggestions.push('1d分辨率应该完全禁用loadMore，只使用WebSocket')
    }
    
    if (stats['1w'] || stats['1M']) {
      suggestions.push('1w和1M分辨率应该优化缓存机制，减少重复调用')
    }
    
    return suggestions
  }
  
  /**
   * 重置统计
   */
  reset() {
    this.callStack.clear()
    console.log('🔄 调试统计已重置')
  }
  
  /**
   * 开始调试会话
   */
  startDebugSession(resolution: string) {
    this.reset()
    console.log(`🚀 开始${resolution}调试会话`, {
      开始时间: new Date().toISOString(),
      分辨率: resolution
    })
  }
  
  /**
   * 结束调试会话
   */
  endDebugSession(resolution: string) {
    const report = this.getDetailedReport()
    
    console.log(`✅ ${resolution}调试会话结束`, {
      结束时间: new Date().toISOString(),
      分辨率: resolution
    })
    
    return report
  }
}

// 全局调试助手实例
export const basicVersionDebugHelper = new BasicVersionDebugHelper()

// 在开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.basicVersionDebugHelper = basicVersionDebugHelper
  console.log('🔧 基本版深度调试助手已启用，使用 window.basicVersionDebugHelper 访问')
}
