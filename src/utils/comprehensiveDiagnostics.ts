/**
 * 综合诊断工具
 * 提供K线系统的全面诊断和报告生成功能
 */

import { KlineLogger } from '~/config/kline.config'
import { commonStore } from '~/stores/commonStore'
import { getUnifiedKlineManager } from '~/utils/unifiedKlineManager'

export interface DiagnosticResult {
  timestamp: number
  overallScore: number
  status: 'healthy' | 'warning' | 'critical'
  categories: {
    performance: DiagnosticCategory
    dataQuality: DiagnosticCategory
    systemHealth: DiagnosticCategory
    unifiedManager: DiagnosticCategory
  }
  recommendations: string[]
  criticalIssues: string[]
  summary: string
}

export interface DiagnosticCategory {
  score: number
  status: 'healthy' | 'warning' | 'critical'
  issues: DiagnosticIssue[]
  metrics: Record<string, any>
}

export interface DiagnosticIssue {
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  recommendation: string
  affected: string[]
}

export class ComprehensiveDiagnostics {
  private logger: any
  private store: any
  private klineManager: any

  constructor() {
    this.logger = KlineLogger.getInstance()
    this.store = commonStore()
    this.klineManager = getUnifiedKlineManager()
  }

  /**
   * 执行全面诊断
   */
  async runComprehensiveDiagnostics(): Promise<DiagnosticResult> {
    const startTime = Date.now()
    this.logger.info('开始全面诊断')

    try {
      // 并行执行各个类别的诊断
      const [performance, dataQuality, systemHealth, unifiedManager] = await Promise.all([
        this.diagnosePerformance(),
        this.diagnoseDataQuality(),
        this.diagnoseSystemHealth(),
        this.diagnoseUnifiedManager()
      ])

      // 计算总体评分
      const overallScore = this.calculateOverallScore(performance, dataQuality, systemHealth, unifiedManager)
      
      // 确定总体状态
      const status = this.determineOverallStatus(overallScore)

      // 收集所有建议和关键问题
      const recommendations = this.collectRecommendations(performance, dataQuality, systemHealth, unifiedManager)
      const criticalIssues = this.collectCriticalIssues(performance, dataQuality, systemHealth, unifiedManager)

      // 生成摘要
      const summary = this.generateSummary(status, overallScore, criticalIssues.length, recommendations.length)

      const result: DiagnosticResult = {
        timestamp: Date.now(),
        overallScore,
        status,
        categories: {
          performance,
          dataQuality,
          systemHealth,
          unifiedManager
        },
        recommendations,
        criticalIssues,
        summary
      }

      const duration = Date.now() - startTime
      this.logger.info('全面诊断完成', { 
        duration, 
        overallScore, 
        status,
        criticalIssuesCount: criticalIssues.length
      })

      return result

    } catch (error) {
      this.logger.error('全面诊断失败', error)
      throw error
    }
  }

  /**
   * 诊断性能状况
   */
  private async diagnosePerformance(): Promise<DiagnosticCategory> {
    const issues: DiagnosticIssue[] = []
    const metrics: Record<string, any> = {}

    try {
      // 获取系统概览
      const systemOverview = this.store.getSystemOverview()
      
      // 检查缓存性能
      const cacheStats = systemOverview.cacheStats
      metrics.cacheHitRate = parseFloat(cacheStats.hitRate.replace('%', ''))
      
      if (metrics.cacheHitRate < 70) {
        issues.push({
          severity: metrics.cacheHitRate < 50 ? 'critical' : 'high',
          title: '缓存命中率过低',
          description: `当前缓存命中率为 ${cacheStats.hitRate}，低于期望的70%`,
          recommendation: '检查缓存策略配置，增加缓存TTL或优化数据访问模式',
          affected: ['数据加载速度', '网络负载', '用户体验']
        })
      }

      // 检查内存使用
      metrics.cacheMemoryUsage = cacheStats.memoryUsage
      if (metrics.cacheMemoryUsage > 100 * 1024 * 1024) { // 100MB
        issues.push({
          severity: 'medium',
          title: '缓存内存使用过高',
          description: `缓存占用内存 ${(metrics.cacheMemoryUsage / 1024 / 1024).toFixed(1)}MB`,
          recommendation: '定期清理过期缓存，优化缓存大小限制',
          affected: ['系统性能', '内存占用']
        })
      }

      // 检查错误恢复状态
      const errorRecovery = systemOverview.errorRecovery
      metrics.activeRetries = errorRecovery.activeRetries
      metrics.criticalErrorCount = errorRecovery.criticalErrorCount

      if (errorRecovery.criticalErrorCount > 5) {
        issues.push({
          severity: 'critical',
          title: '关键错误数量过多',
          description: `发现 ${errorRecovery.criticalErrorCount} 个关键错误`,
          recommendation: '检查错误日志，修复导致错误的根本原因',
          affected: ['系统稳定性', '数据可靠性']
        })
      }

    } catch (error) {
      issues.push({
        severity: 'critical',
        title: '性能诊断失败',
        description: `无法获取性能指标: ${error.message}`,
        recommendation: '检查系统监控组件是否正常工作',
        affected: ['监控能力', '问题排查']
      })
    }

    const score = this.calculateCategoryScore(issues)
    return {
      score,
      status: this.determineStatus(score),
      issues,
      metrics
    }
  }

  /**
   * 诊断数据质量
   */
  private async diagnoseDataQuality(): Promise<DiagnosticCategory> {
    const issues: DiagnosticIssue[] = []
    const metrics: Record<string, any> = {}

    try {
      const dataQuality = this.store.globalDataQuality
      
      // 计算连接成功率
      const connectionSuccessRate = dataQuality.totalSocketConnections > 0 
        ? (dataQuality.successfulSocketConnections / dataQuality.totalSocketConnections) * 100
        : 100
      
      metrics.connectionSuccessRate = connectionSuccessRate
      metrics.totalConnections = dataQuality.totalSocketConnections
      metrics.websocketErrors = dataQuality.websocketErrors
      metrics.klineDataErrors = dataQuality.klineDataErrors
      metrics.dataLossEvents = dataQuality.dataLossEvents
      metrics.recoveryEvents = dataQuality.recoveryEvents

      // 检查连接成功率
      if (connectionSuccessRate < 95) {
        issues.push({
          severity: connectionSuccessRate < 80 ? 'critical' : 'high',
          title: 'WebSocket连接成功率过低',
          description: `连接成功率为 ${connectionSuccessRate.toFixed(1)}%，低于95%的标准`,
          recommendation: '检查网络连接、服务器状态和WebSocket配置',
          affected: ['实时数据更新', '用户体验']
        })
      }

      // 检查数据错误率
      const totalErrors = dataQuality.websocketErrors + dataQuality.klineDataErrors + dataQuality.priceDataErrors
      const errorRate = dataQuality.totalSocketConnections > 0 
        ? (totalErrors / dataQuality.totalSocketConnections) * 100
        : 0
      
      metrics.errorRate = errorRate

      if (errorRate > 5) {
        issues.push({
          severity: errorRate > 15 ? 'critical' : 'high',
          title: '数据错误率过高',
          description: `数据错误率为 ${errorRate.toFixed(1)}%，超过5%的阈值`,
          recommendation: '检查数据源质量、网络稳定性和数据验证逻辑',
          affected: ['数据准确性', '图表显示']
        })
      }

      // 检查数据丢失事件
      if (dataQuality.dataLossEvents > 10) {
        issues.push({
          severity: 'medium',
          title: '数据丢失事件较多',
          description: `发生了 ${dataQuality.dataLossEvents} 次数据丢失事件`,
          recommendation: '优化网络连接稳定性，增强数据缓冲机制',
          affected: ['数据完整性', '图表连续性']
        })
      }

      // 检查恢复能力
      const recoveryRate = dataQuality.dataLossEvents > 0 
        ? (dataQuality.recoveryEvents / dataQuality.dataLossEvents) * 100
        : 100
      
      metrics.recoveryRate = recoveryRate

      if (recoveryRate < 80) {
        issues.push({
          severity: 'medium',
          title: '数据恢复率偏低',
          description: `数据恢复率为 ${recoveryRate.toFixed(1)}%，建议维持在80%以上`,
          recommendation: '改进错误恢复机制，增强自动修复能力',
          affected: ['系统韧性', '用户体验']
        })
      }

    } catch (error) {
      issues.push({
        severity: 'critical',
        title: '数据质量诊断失败',
        description: `无法获取数据质量指标: ${error.message}`,
        recommendation: '检查数据质量监控模块是否正常运行',
        affected: ['数据监控', '质量保证']
      })
    }

    const score = this.calculateCategoryScore(issues)
    return {
      score,
      status: this.determineStatus(score),
      issues,
      metrics
    }
  }

  /**
   * 诊断系统健康状况
   */
  private async diagnoseSystemHealth(): Promise<DiagnosticCategory> {
    const issues: DiagnosticIssue[] = []
    const metrics: Record<string, any> = {}

    try {
      // 检查数据处理器数量
      const systemOverview = this.store.getSystemOverview()
      const processorCount = systemOverview.dataProcessors.activeProcessors
      
      metrics.activeProcessors = processorCount
      metrics.availableResolutions = systemOverview.dataProcessors.availableResolutions

      if (processorCount === 0) {
        issues.push({
          severity: 'critical',
          title: '没有活跃的数据处理器',
          description: '系统中没有活跃的K线数据处理器',
          recommendation: '检查数据处理器初始化逻辑，确保正确创建处理器实例',
          affected: ['数据处理', '系统功能']
        })
      }

      // 检查缓存冲突
      const conflictReport = this.store.getCacheConflictReport()
      metrics.cacheConflicts = conflictReport.totalConflicts
      metrics.activeCacheConflicts = conflictReport.activeConflicts

      if (conflictReport.activeConflicts > 0) {
        issues.push({
          severity: 'medium',
          title: '存在缓存冲突',
          description: `发现 ${conflictReport.activeConflicts} 个活跃的缓存冲突`,
          recommendation: '检查缓存命名空间隔离配置，清理冲突的缓存条目',
          affected: ['数据一致性', '性能表现']
        })
      }

      // 检查健康检查时间
      const lastHealthCheck = this.store.globalDataQuality.lastHealthCheck
      const healthCheckDelay = Date.now() - lastHealthCheck
      
      metrics.healthCheckDelay = healthCheckDelay

      if (healthCheckDelay > 300000) { // 5分钟
        issues.push({
          severity: 'medium',
          title: '健康检查延迟',
          description: `上次健康检查距今 ${Math.round(healthCheckDelay / 60000)} 分钟`,
          recommendation: '检查健康检查定时器是否正常运行',
          affected: ['系统监控', '问题发现']
        })
      }

    } catch (error) {
      issues.push({
        severity: 'critical',
        title: '系统健康诊断失败',
        description: `无法执行系统健康检查: ${error.message}`,
        recommendation: '检查系统监控模块的完整性',
        affected: ['系统监控', '健康状态']
      })
    }

    const score = this.calculateCategoryScore(issues)
    return {
      score,
      status: this.determineStatus(score),
      issues,
      metrics
    }
  }

  /**
   * 诊断统一管理器状况
   */
  private async diagnoseUnifiedManager(): Promise<DiagnosticCategory> {
    const issues: DiagnosticIssue[] = []
    const metrics: Record<string, any> = {}

    try {
      if (!this.klineManager) {
        issues.push({
          severity: 'critical',
          title: '统一管理器未初始化',
          description: '统一K线数据管理器未正确初始化',
          recommendation: '检查管理器初始化代码，确保在组件挂载时正确创建',
          affected: ['版本切换', '数据协调']
        })
        
        const score = 0
        return {
          score,
          status: 'critical',
          issues,
          metrics
        }
      }

      const managerState = this.klineManager.getState()
      const overview = this.klineManager.getSystemOverview()

      metrics.currentVersion = managerState.currentVersion
      metrics.dataQualityScore = (1 - managerState.dataQuality.errorRate) * 100
      metrics.averageLatency = managerState.performance.averageLatency
      metrics.cacheHitRate = managerState.performance.cacheHitRate
      metrics.switchingTime = managerState.performance.switchingTime
      metrics.activeProcessors = overview.activeProcessors
      metrics.subscriberCount = overview.subscriberCount

      // 检查数据质量
      if (managerState.dataQuality.errorRate > 0.05) {
        issues.push({
          severity: 'high',
          title: '管理器数据质量问题',
          description: `数据错误率为 ${(managerState.dataQuality.errorRate * 100).toFixed(1)}%`,
          recommendation: '检查数据源质量和处理逻辑',
          affected: ['数据准确性', '版本一致性']
        })
      }

      // 检查同步状态
      const syncStatus = managerState.syncStatus
      if (!syncStatus.crossVersionConsistency) {
        issues.push({
          severity: 'medium',
          title: '版本间数据不一致',
          description: '检测到基础版和专业版之间数据不一致',
          recommendation: '执行数据同步修复，检查缓存隔离配置',
          affected: ['版本切换', '数据一致性']
        })
      }

      if (syncStatus.basicVersion === 'error' || syncStatus.professionalVersion === 'error') {
        issues.push({
          severity: 'high',
          title: '版本同步错误',
          description: '基础版或专业版存在同步错误',
          recommendation: '重新初始化有问题的版本，检查错误日志',
          affected: ['版本功能', '用户体验']
        })
      }

      // 检查性能指标
      if (managerState.performance.switchingTime > 3000) {
        issues.push({
          severity: 'medium',
          title: '版本切换耗时过长',
          description: `平均切换时间为 ${managerState.performance.switchingTime.toFixed(0)}ms`,
          recommendation: '优化版本切换逻辑，减少数据预加载时间',
          affected: ['用户体验', '响应速度']
        })
      }

      if (managerState.performance.averageLatency > 1000) {
        issues.push({
          severity: 'medium',
          title: '网络延迟过高',
          description: `平均网络延迟为 ${managerState.performance.averageLatency.toFixed(0)}ms`,
          recommendation: '检查网络连接质量，优化数据传输',
          affected: ['实时性', '用户体验']
        })
      }

      // 检查订阅者数量
      if (overview.subscriberCount === 0) {
        issues.push({
          severity: 'medium',
          title: '没有活跃订阅者',
          description: '统一管理器没有活跃的事件订阅者',
          recommendation: '检查事件订阅逻辑，确保组件正确订阅管理器事件',
          affected: ['事件通知', '状态同步']
        })
      }

    } catch (error) {
      issues.push({
        severity: 'critical',
        title: '统一管理器诊断失败',
        description: `无法诊断统一管理器: ${error.message}`,
        recommendation: '检查统一管理器实现和初始化流程',
        affected: ['管理器功能', '系统协调']
      })
    }

    const score = this.calculateCategoryScore(issues)
    return {
      score,
      status: this.determineStatus(score),
      issues,
      metrics
    }
  }

  /**
   * 计算类别评分
   */
  private calculateCategoryScore(issues: DiagnosticIssue[]): number {
    if (issues.length === 0) return 100

    let totalPenalty = 0
    for (const issue of issues) {
      switch (issue.severity) {
        case 'low': totalPenalty += 5; break
        case 'medium': totalPenalty += 15; break
        case 'high': totalPenalty += 30; break
        case 'critical': totalPenalty += 50; break
      }
    }

    return Math.max(0, 100 - totalPenalty)
  }

  /**
   * 确定状态
   */
  private determineStatus(score: number): 'healthy' | 'warning' | 'critical' {
    if (score >= 80) return 'healthy'
    if (score >= 60) return 'warning'
    return 'critical'
  }

  /**
   * 计算总体评分
   */
  private calculateOverallScore(...categories: DiagnosticCategory[]): number {
    const weights = [0.3, 0.3, 0.2, 0.2] // performance, dataQuality, systemHealth, unifiedManager
    let weightedSum = 0
    
    categories.forEach((category, index) => {
      weightedSum += category.score * weights[index]
    })
    
    return Math.round(weightedSum)
  }

  /**
   * 确定总体状态
   */
  private determineOverallStatus(score: number): 'healthy' | 'warning' | 'critical' {
    if (score >= 85) return 'healthy'
    if (score >= 70) return 'warning'
    return 'critical'
  }

  /**
   * 收集建议
   */
  private collectRecommendations(...categories: DiagnosticCategory[]): string[] {
    const recommendations: string[] = []
    
    categories.forEach(category => {
      category.issues.forEach(issue => {
        if (issue.severity === 'high' || issue.severity === 'critical') {
          recommendations.push(`${issue.title}: ${issue.recommendation}`)
        }
      })
    })
    
    return recommendations
  }

  /**
   * 收集关键问题
   */
  private collectCriticalIssues(...categories: DiagnosticCategory[]): string[] {
    const criticalIssues: string[] = []
    
    categories.forEach(category => {
      category.issues.forEach(issue => {
        if (issue.severity === 'critical') {
          criticalIssues.push(issue.title)
        }
      })
    })
    
    return criticalIssues
  }

  /**
   * 生成摘要
   */
  private generateSummary(status: string, score: number, criticalCount: number, recommendationCount: number): string {
    let summary = `系统整体状况: ${this.getStatusText(status)}，综合评分: ${score}/100。`
    
    if (criticalCount > 0) {
      summary += ` 发现 ${criticalCount} 个关键问题需要立即处理。`
    }
    
    if (recommendationCount > 0) {
      summary += ` 提供了 ${recommendationCount} 条优化建议。`
    } else {
      summary += ` 系统运行良好，无需特别关注。`
    }
    
    return summary
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    switch (status) {
      case 'healthy': return '健康'
      case 'warning': return '警告'
      case 'critical': return '严重'
      default: return '未知'
    }
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport(result: DiagnosticResult): string {
    const { timestamp, overallScore, status, categories, recommendations, criticalIssues, summary } = result
    
    const statusColor = status === 'healthy' ? '#4CAF50' : status === 'warning' ? '#FF9800' : '#F44336'
    const date = new Date(timestamp).toLocaleString('zh-CN')
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线系统综合诊断报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
        .score { font-size: 48px; font-weight: bold; color: ${statusColor}; margin: 10px 0; }
        .status { font-size: 24px; color: ${statusColor}; text-transform: uppercase; }
        .summary { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; line-height: 1.6; }
        .categories { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0; }
        .category { border: 1px solid #ddd; border-radius: 6px; padding: 20px; }
        .category h3 { margin-top: 0; color: #333; }
        .category-score { font-size: 24px; font-weight: bold; margin: 10px 0; }
        .healthy { color: #4CAF50; }
        .warning { color: #FF9800; }
        .critical { color: #F44336; }
        .issues { margin-top: 15px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; background: #f9f9f9; }
        .issue.high { border-left-color: #FF9800; }
        .issue.critical { border-left-color: #F44336; }
        .recommendations, .critical-issues { margin: 30px 0; }
        .recommendations h2, .critical-issues h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .list { background: #f8f9fa; padding: 20px; border-radius: 6px; }
        .list li { margin: 8px 0; line-height: 1.4; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>K线系统综合诊断报告</h1>
            <div class="score">${overallScore}</div>
            <div class="status">${this.getStatusText(status)}</div>
            <p>生成时间: ${date}</p>
        </div>
        
        <div class="summary">
            <h2>诊断摘要</h2>
            <p>${summary}</p>
        </div>
        
        <div class="categories">
            ${Object.entries(categories).map(([name, category]) => `
            <div class="category">
                <h3>${this.getCategoryName(name)}</h3>
                <div class="category-score ${category.status}">${category.score}/100</div>
                <div class="issues">
                    ${category.issues.map(issue => `
                    <div class="issue ${issue.severity}">
                        <strong>${issue.title}</strong><br>
                        ${issue.description}<br>
                        <em>建议: ${issue.recommendation}</em>
                    </div>
                    `).join('')}
                </div>
            </div>
            `).join('')}
        </div>
        
        ${criticalIssues.length > 0 ? `
        <div class="critical-issues">
            <h2>关键问题 (${criticalIssues.length})</h2>
            <div class="list">
                <ul>
                    ${criticalIssues.map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
        </div>
        ` : ''}
        
        ${recommendations.length > 0 ? `
        <div class="recommendations">
            <h2>优化建议 (${recommendations.length})</h2>
            <div class="list">
                <ul>
                    ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        </div>
        ` : ''}
        
        <div class="footer">
            <p>报告由K线系统综合诊断工具自动生成</p>
        </div>
    </div>
</body>
</html>
    `
  }

  /**
   * 获取类别名称
   */
  private getCategoryName(name: string): string {
    const names = {
      performance: '性能表现',
      dataQuality: '数据质量',
      systemHealth: '系统健康',
      unifiedManager: '统一管理器'
    }
    return names[name] || name
  }
}

// 创建全局实例
let globalDiagnostics: ComprehensiveDiagnostics | null = null

export const createComprehensiveDiagnostics = (): ComprehensiveDiagnostics => {
  if (!globalDiagnostics) {
    globalDiagnostics = new ComprehensiveDiagnostics()
  }
  return globalDiagnostics
}

export const getComprehensiveDiagnostics = (): ComprehensiveDiagnostics | null => {
  return globalDiagnostics
}

// 便捷函数
export const runQuickDiagnostics = async (): Promise<DiagnosticResult> => {
  const diagnostics = createComprehensiveDiagnostics()
  return await diagnostics.runComprehensiveDiagnostics()
}

export const generateDiagnosticReport = (result: DiagnosticResult): string => {
  const diagnostics = createComprehensiveDiagnostics()
  return diagnostics.generateHTMLReport(result)
}