/**
 * 统一K线数据管理器
 * 负责协调基础版和专业版之间的数据流，确保数据一致性和版本切换的无缝体验
 */

import { ref, reactive, watch, computed } from 'vue'
import { KlineLogger } from '~/config/kline.config'
import { createKlineProcessor, validateKlineData, type KlineBarData } from '~/utils/klineDataProcessor'
import { commonStore } from '~/stores/commonStore'

export interface KlineVersionConfig {
  version: 'basic' | 'professional'
  namespace: string
  dataSource: 'api' | 'websocket' | 'hybrid'
  cacheTTL: number
  features: {
    realTimeUpdate: boolean
    seamlessConnection: boolean
    dataValidation: boolean
    errorRecovery: boolean
  }
}

export interface UnifiedKlineState {
  currentVersion: 'basic' | 'professional'
  activeData: KlineBarData[]
  dataQuality: {
    totalUpdates: number
    validUpdates: number
    errorRate: number
    lastUpdate: number
  }
  syncStatus: {
    basicVersion: 'synced' | 'syncing' | 'error' | 'idle'
    professionalVersion: 'synced' | 'syncing' | 'error' | 'idle'
    crossVersionConsistency: boolean
  }
  performance: {
    averageLatency: number
    cacheHitRate: number
    switchingTime: number
  }
}

export class UnifiedKlineManager {
  private logger: any
  private store: any
  private dataProcessors: Map<string, any>
  private versionConfigs: Map<string, KlineVersionConfig>
  private state: UnifiedKlineState
  private subscribers: Map<string, Function[]>
  private dataBuffer: Map<string, KlineBarData[]>
  private switchingLock: boolean
  private performanceMetrics: any

  constructor() {
    this.logger = KlineLogger.getInstance()
    this.store = commonStore()
    this.dataProcessors = new Map()
    this.versionConfigs = new Map()
    this.subscribers = new Map()
    this.dataBuffer = new Map()
    this.switchingLock = false
    
    this.state = reactive({
      currentVersion: 'basic',
      activeData: [],
      dataQuality: {
        totalUpdates: 0,
        validUpdates: 0,
        errorRate: 0,
        lastUpdate: 0
      },
      syncStatus: {
        basicVersion: 'idle',
        professionalVersion: 'idle',
        crossVersionConsistency: true
      },
      performance: {
        averageLatency: 0,
        cacheHitRate: 0,
        switchingTime: 0
      }
    })

    this.initializeVersionConfigs()
    this.setupPerformanceTracking()
    this.logger.info('统一K线数据管理器初始化完成')
  }

  /**
   * 初始化版本配置
   */
  private initializeVersionConfigs() {
    // 基础版配置
    this.versionConfigs.set('basic', {
      version: 'basic',
      namespace: 'basic',
      dataSource: 'hybrid',
      cacheTTL: 180000, // 3分钟
      features: {
        realTimeUpdate: true,
        seamlessConnection: true,
        dataValidation: true,
        errorRecovery: true
      }
    })

    // 专业版配置
    this.versionConfigs.set('professional', {
      version: 'professional',
      namespace: 'professional',
      dataSource: 'websocket',
      cacheTTL: 300000, // 5分钟
      features: {
        realTimeUpdate: true,
        seamlessConnection: true,
        dataValidation: true,
        errorRecovery: true
      }
    })

    this.logger.debug('版本配置初始化完成', {
      basicConfig: this.versionConfigs.get('basic'),
      professionalConfig: this.versionConfigs.get('professional')
    })
  }

  /**
   * 设置性能追踪
   */
  private setupPerformanceTracking() {
    this.performanceMetrics = {
      latencyBuffer: [],
      cacheOperations: { hits: 0, misses: 0 },
      switchingTimes: [],
      lastMetricsUpdate: Date.now()
    }

    // 定期更新性能指标
    setInterval(() => {
      this.updatePerformanceMetrics()
    }, 10000) // 每10秒更新一次
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics() {
    const metrics = this.performanceMetrics
    
    // 计算平均延迟
    if (metrics.latencyBuffer.length > 0) {
      this.state.performance.averageLatency = 
        metrics.latencyBuffer.reduce((sum, val) => sum + val, 0) / metrics.latencyBuffer.length
      
      // 保持缓冲区大小合理
      if (metrics.latencyBuffer.length > 100) {
        metrics.latencyBuffer = metrics.latencyBuffer.slice(-50)
      }
    }

    // 计算缓存命中率
    const totalCacheOps = metrics.cacheOperations.hits + metrics.cacheOperations.misses
    if (totalCacheOps > 0) {
      this.state.performance.cacheHitRate = 
        (metrics.cacheOperations.hits / totalCacheOps) * 100
    }

    // 计算平均切换时间
    if (metrics.switchingTimes.length > 0) {
      this.state.performance.switchingTime = 
        metrics.switchingTimes.reduce((sum, val) => sum + val, 0) / metrics.switchingTimes.length
      
      if (metrics.switchingTimes.length > 20) {
        metrics.switchingTimes = metrics.switchingTimes.slice(-10)
      }
    }

    this.logger.debug('性能指标已更新', this.state.performance)
  }

  /**
   * 获取或创建数据处理器
   */
  private getDataProcessor(resolution: string): any {
    if (!this.dataProcessors.has(resolution)) {
      this.dataProcessors.set(resolution, createKlineProcessor(resolution))
      this.logger.debug('创建数据处理器', { resolution })
    }
    return this.dataProcessors.get(resolution)
  }

  /**
   * 记录延迟指标
   */
  private recordLatency(startTime: number) {
    const latency = Date.now() - startTime
    this.performanceMetrics.latencyBuffer.push(latency)
  }

  /**
   * 记录缓存操作
   */
  private recordCacheOperation(isHit: boolean) {
    if (isHit) {
      this.performanceMetrics.cacheOperations.hits++
    } else {
      this.performanceMetrics.cacheOperations.misses++
    }
  }

  /**
   * 切换K线版本
   */
  async switchVersion(targetVersion: 'basic' | 'professional', pair: string, resolution: string): Promise<boolean> {
    if (this.switchingLock) {
      this.logger.warn('版本切换正在进行中，忽略此次请求')
      return false
    }

    const startTime = Date.now()
    this.switchingLock = true

    try {
      this.logger.info('开始版本切换', {
        from: this.state.currentVersion,
        to: targetVersion,
        pair,
        resolution
      })

      // 更新同步状态
      this.state.syncStatus[`${targetVersion}Version`] = 'syncing'
      
      // 预加载目标版本数据
      const preloadSuccess = await this.preloadVersionData(targetVersion, pair, resolution)
      if (!preloadSuccess) {
        throw new Error(`${targetVersion}版本数据预加载失败`)
      }

      // 执行数据一致性检查
      const consistencyCheck = await this.performCrossVersionConsistencyCheck(pair, resolution)
      if (!consistencyCheck) {
        this.logger.warn('版本间数据一致性检查失败，继续切换但可能影响体验')
      }

      // 执行版本切换
      await this.performVersionSwitch(targetVersion, pair, resolution)

      // 更新状态
      this.state.currentVersion = targetVersion
      this.state.syncStatus[`${targetVersion}Version`] = 'synced'
      this.state.syncStatus.crossVersionConsistency = consistencyCheck

      const switchingTime = Date.now() - startTime
      this.performanceMetrics.switchingTimes.push(switchingTime)

      this.logger.info('版本切换完成', {
        targetVersion,
        switchingTime,
        consistencyCheck,
        pair,
        resolution
      })

      // 通知订阅者
      this.notifySubscribers('version_switched', {
        version: targetVersion,
        pair,
        resolution,
        switchingTime,
        success: true
      })

      return true

    } catch (error) {
      this.logger.error('版本切换失败', {
        targetVersion,
        pair,
        resolution,
        error: error.message
      })

      // 恢复同步状态
      this.state.syncStatus[`${targetVersion}Version`] = 'error'
      
      // 通知订阅者切换失败
      this.notifySubscribers('version_switch_failed', {
        version: targetVersion,
        pair,
        resolution,
        error: error.message
      })

      return false

    } finally {
      this.switchingLock = false
    }
  }

  /**
   * 预加载版本数据
   */
  private async preloadVersionData(version: 'basic' | 'professional', pair: string, resolution: string): Promise<boolean> {
    try {
      const config = this.versionConfigs.get(version)
      if (!config) {
        throw new Error(`未找到${version}版本配置`)
      }

      // 检查缓存
      const cacheKey = `${pair}_${resolution}_${version}`
      const cachedData = this.store.getCacheEntry('kline', cacheKey, config.namespace)
      
      if (cachedData) {
        this.recordCacheOperation(true)
        this.logger.debug('版本数据缓存命中', { version, pair, resolution })
        return true
      }

      this.recordCacheOperation(false)

      // 根据版本特性获取数据
      if (config.dataSource === 'hybrid' || config.dataSource === 'websocket') {
        // 通过WebSocket获取数据
        await this.store.getKlineSocket(pair, resolution)
        
        // 缓存数据
        this.store.manageCacheEntry('kline', cacheKey, {
          klineList: this.store.klineList,
          klineTicker: this.store.klineTicker,
          version,
          timestamp: Date.now()
        }, config.cacheTTL, config.namespace)

        this.logger.debug('版本数据预加载完成', { version, pair, resolution })
        return true
      }

      return false

    } catch (error) {
      this.logger.error('版本数据预加载失败', {
        version,
        pair,
        resolution,
        error: error.message
      })
      return false
    }
  }

  /**
   * 执行跨版本一致性检查
   */
  private async performCrossVersionConsistencyCheck(pair: string, resolution: string): Promise<boolean> {
    try {
      // 获取两个版本的最新数据进行比较
      const basicData = this.store.getCacheEntry('kline', `${pair}_${resolution}_basic`, 'basic')
      const professionalData = this.store.getCacheEntry('kline', `${pair}_${resolution}_professional`, 'professional')

      if (!basicData || !professionalData) {
        this.logger.debug('跨版本一致性检查：缺少对比数据')
        return true // 没有数据对比，认为一致
      }

      // 比较最新价格
      const basicLatest = basicData.klineList?.[basicData.klineList.length - 1]
      const professionalLatest = professionalData.klineList?.[professionalData.klineList.length - 1]

      if (basicLatest && professionalLatest) {
        const priceDiff = Math.abs(basicLatest.close - professionalLatest.close)
        const priceThreshold = basicLatest.close * 0.001 // 0.1%的容错率

        if (priceDiff > priceThreshold) {
          this.logger.warn('跨版本价格不一致', {
            basicPrice: basicLatest.close,
            professionalPrice: professionalLatest.close,
            difference: priceDiff,
            threshold: priceThreshold
          })
          return false
        }
      }

      this.logger.debug('跨版本一致性检查通过')
      return true

    } catch (error) {
      this.logger.error('跨版本一致性检查失败', error)
      return false
    }
  }

  /**
   * 执行版本切换
   */
  private async performVersionSwitch(targetVersion: 'basic' | 'professional', pair: string, resolution: string): Promise<void> {
    const config = this.versionConfigs.get(targetVersion)
    if (!config) {
      throw new Error(`未找到${targetVersion}版本配置`)
    }

    // 清理当前版本的订阅
    if (this.state.currentVersion !== targetVersion) {
      await this.cleanupCurrentVersion(pair, resolution)
    }

    // 初始化目标版本
    await this.initializeTargetVersion(targetVersion, pair, resolution)

    this.logger.debug('版本切换执行完成', { targetVersion, pair, resolution })
  }

  /**
   * 清理当前版本
   */
  private async cleanupCurrentVersion(pair: string, resolution: string): Promise<void> {
    try {
      // 取消当前版本的订阅
      this.store.cancelKline(pair, resolution)
      
      // 清理版本特定的缓存
      const currentConfig = this.versionConfigs.get(this.state.currentVersion)
      if (currentConfig) {
        this.store.cleanupExpiredCache(currentConfig.namespace)
      }

      this.logger.debug('当前版本清理完成', { version: this.state.currentVersion })

    } catch (error) {
      this.logger.warn('当前版本清理失败', error)
    }
  }

  /**
   * 初始化目标版本
   */
  private async initializeTargetVersion(targetVersion: 'basic' | 'professional', pair: string, resolution: string): Promise<void> {
    const config = this.versionConfigs.get(targetVersion)
    if (!config) {
      throw new Error(`未找到${targetVersion}版本配置`)
    }

    // 根据目标版本的特性进行初始化
    if (config.features.realTimeUpdate) {
      await this.store.getKlineSocket(pair, resolution)
    }

    this.logger.debug('目标版本初始化完成', { targetVersion, pair, resolution })
  }

  /**
   * 订阅事件
   */
  subscribe(event: string, callback: Function): () => void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, [])
    }
    
    this.subscribers.get(event)!.push(callback)
    
    // 返回取消订阅函数
    return () => {
      const callbacks = this.subscribers.get(event)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(event: string, data: any): void {
    const callbacks = this.subscribers.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          this.logger.error('订阅者回调执行失败', { event, error })
        }
      })
    }
  }

  /**
   * 获取当前状态
   */
  getState(): UnifiedKlineState {
    return this.state
  }

  /**
   * 获取系统概览
   */
  getSystemOverview() {
    return {
      currentVersion: this.state.currentVersion,
      dataQuality: this.state.dataQuality,
      syncStatus: this.state.syncStatus,
      performance: this.state.performance,
      activeProcessors: this.dataProcessors.size,
      subscriberCount: Array.from(this.subscribers.values()).reduce((sum, arr) => sum + arr.length, 0),
      cacheStatus: {
        basic: this.store.getCacheEntry('kline', 'status', 'basic') ? 'active' : 'inactive',
        professional: this.store.getCacheEntry('kline', 'status', 'professional') ? 'active' : 'inactive'
      }
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    // 清理所有订阅者
    this.subscribers.clear()
    
    // 清理数据处理器
    this.dataProcessors.clear()
    
    // 清理数据缓冲区
    this.dataBuffer.clear()
    
    this.logger.info('统一K线数据管理器已销毁')
  }
}

// 创建全局实例
let globalKlineManager: UnifiedKlineManager | null = null

export const createUnifiedKlineManager = (): UnifiedKlineManager => {
  if (!globalKlineManager) {
    globalKlineManager = new UnifiedKlineManager()
  }
  return globalKlineManager
}

export const getUnifiedKlineManager = (): UnifiedKlineManager | null => {
  return globalKlineManager
}

export const destroyUnifiedKlineManager = (): void => {
  if (globalKlineManager) {
    globalKlineManager.destroy()
    globalKlineManager = null
  }
}