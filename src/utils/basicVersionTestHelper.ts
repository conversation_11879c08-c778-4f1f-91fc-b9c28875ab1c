/**
 * 基本版1w和1M修复效果测试助手
 * 用于验证基本版修复后的行为是否正确
 */

export class BasicVersionTestHelper {
  private requestCounts = new Map<string, number>()
  private loadMoreCounts = new Map<string, number>()
  private originalFetch: typeof fetch
  
  constructor() {
    this.originalFetch = window.fetch
    this.setupMonitoring()
  }
  
  /**
   * 设置监控
   */
  private setupMonitoring() {
    // 监控API请求
    window.fetch = async (...args) => {
      const url = args[0] as string
      if (url.includes('/api/v1/order/klines')) {
        const urlObj = new URL(url, window.location.origin)
        const timeFrame = urlObj.searchParams.get('time_frame')
        
        if (timeFrame === '1w' || timeFrame === '1M') {
          const key = `${timeFrame}-basic-api`
          this.requestCounts.set(key, (this.requestCounts.get(key) || 0) + 1)
          
          console.log(`🔍 基本版${timeFrame}接口调用`, {
            调用次数: this.requestCounts.get(key),
            URL: url,
            时间: new Date().toISOString()
          })
        }
      }
      
      return this.originalFetch.apply(window, args)
    }
    
    // 监控loadMore回调
    this.monitorLoadMoreCallbacks()
  }
  
  /**
   * 监控loadMore回调
   */
  private monitorLoadMoreCallbacks() {
    // 通过代理console.log来监控loadMore日志
    const originalLog = console.log
    console.log = (...args) => {
      const message = args.join(' ')
      if (message.includes('基本版loadMore:')) {
        const match = message.match(/基本版loadMore: (1w|1M)/)
        if (match) {
          const resolution = match[1]
          const key = `${resolution}-loadMore`
          this.loadMoreCounts.set(key, (this.loadMoreCounts.get(key) || 0) + 1)
          
          console.warn(`🔄 基本版${resolution}loadMore触发`, {
            触发次数: this.loadMoreCounts.get(key),
            消息: message
          })
        }
      }
      
      return originalLog.apply(console, args)
    }
  }
  
  /**
   * 获取测试报告
   */
  getTestReport() {
    const report = {
      基本版API请求统计: Object.fromEntries(this.requestCounts),
      基本版LoadMore统计: Object.fromEntries(this.loadMoreCounts),
      测试时间: new Date().toISOString(),
      修复状态: {
        '1w基本版API调用次数': this.requestCounts.get('1w-basic-api') || 0,
        '1M基本版API调用次数': this.requestCounts.get('1M-basic-api') || 0,
        '1w基本版LoadMore次数': this.loadMoreCounts.get('1w-loadMore') || 0,
        '1M基本版LoadMore次数': this.loadMoreCounts.get('1M-loadMore') || 0
      }
    }
    
    // 判断修复是否成功
    const is1wApiFixed = (this.requestCounts.get('1w-basic-api') || 0) <= 2 // 允许最多2次请求
    const is1MApiFixed = (this.requestCounts.get('1M-basic-api') || 0) <= 2
    const is1wLoadMoreFixed = (this.loadMoreCounts.get('1w-loadMore') || 0) <= 3 // 允许最多3次loadMore
    const is1MLoadMoreFixed = (this.loadMoreCounts.get('1M-loadMore') || 0) <= 3
    
    report.修复状态['基本版修复成功'] = is1wApiFixed && is1MApiFixed && is1wLoadMoreFixed && is1MLoadMoreFixed
    
    console.log('📊 基本版1w和1M修复效果测试报告', report)
    
    return report
  }
  
  /**
   * 重置统计
   */
  reset() {
    this.requestCounts.clear()
    this.loadMoreCounts.clear()
    console.log('🔄 基本版测试统计已重置')
  }
  
  /**
   * 开始测试会话
   */
  startTestSession(resolution: '1w' | '1M') {
    this.reset()
    console.log(`🚀 开始基本版${resolution}测试会话`, {
      开始时间: new Date().toISOString(),
      分辨率: resolution
    })
  }
  
  /**
   * 结束测试会话
   */
  endTestSession(resolution: '1w' | '1M') {
    const report = this.getTestReport()
    
    console.log(`✅ 基本版${resolution}测试会话结束`, {
      结束时间: new Date().toISOString(),
      分辨率: resolution,
      测试结果: report.修复状态
    })
    
    return report
  }
  
  /**
   * 恢复原始fetch
   */
  restore() {
    window.fetch = this.originalFetch
    console.log('🔧 基本版监控已恢复')
  }
}

// 全局基本版测试助手实例
export const basicVersionTestHelper = new BasicVersionTestHelper()

// 在开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.basicVersionTestHelper = basicVersionTestHelper
  console.log('🔧 基本版1w和1M测试助手已启用，使用 window.basicVersionTestHelper 访问')
}
