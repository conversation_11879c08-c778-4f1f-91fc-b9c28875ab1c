export interface PriceUpdateConfig {
  updateThreshold?: number
  batchUpdateDelay?: number
  enableSmartThrottling?: boolean
  maxUpdateFrequency?: number
}

export interface TickerData {
  last?: number | string
  change?: number | string
  high?: number | string
  low?: number | string
  amount?: number | string
  volume?: number | string
  product?: string
  [key: string]: any
}

export interface TradeData {
  p?: number | string
  q?: number | string
  s?: number | string
  t?: number
  i?: string | number
  [key: string]: any
}

export const usePriceUpdateOptimizer = (config: PriceUpdateConfig = {}) => {
  const {
    updateThreshold = 0.00001,  // 降低阈值，确保微小价格变化也能更新
    batchUpdateDelay = 8,       // 减少延迟，提高响应速度
    enableSmartThrottling = false, // 禁用智能节流，确保价格实时更新
    maxUpdateFrequency = 120    // 增加最大更新频率
  } = config

  const updateQueue = new Map<string, TickerData>()
  const lastUpdateTime = new Map<string, number>()
  const changeFlags = ref(new Set<string>())
  let batchUpdateTimer: number | null = null

  const hasSignificantChange = (oldData: TickerData | undefined, newData: TickerData): boolean => {
    if (!oldData) return true

    const numericFields = ['last', 'change', 'high', 'low', 'amount', 'volume']
    
    for (const field of numericFields) {
      const oldVal = Number(oldData[field]) || 0
      const newVal = Number(newData[field]) || 0
      
      if (Math.abs(oldVal - newVal) > updateThreshold) {
        return true
      }
    }
    
    return false
  }

  const shouldUpdate = (product: string): boolean => {
    if (!enableSmartThrottling) return true

    const now = Date.now()
    const lastUpdate = lastUpdateTime.get(product) || 0
    const minInterval = 1000 / maxUpdateFrequency

    return (now - lastUpdate) >= minInterval
  }

  const queueUpdate = (product: string, data: TickerData) => {
    updateQueue.set(product, data)
    changeFlags.value.add(product)

    if (batchUpdateTimer) {
      clearTimeout(batchUpdateTimer)
    }

    batchUpdateTimer = setTimeout(() => {
      processBatchUpdates()
    }, batchUpdateDelay) as unknown as number
  }

  const processBatchUpdates = () => {
    const updates = new Map(updateQueue)
    updateQueue.clear()
    
    const newChangeFlags = new Set<string>()
    
    for (const [product, data] of updates) {
      if (shouldUpdate(product)) {
        lastUpdateTime.set(product, Date.now())
        newChangeFlags.add(product)
      }
    }
    
    changeFlags.value = newChangeFlags
    batchUpdateTimer = null
  }

  const markAsUpdated = (product: string) => {
    changeFlags.value.delete(product)
    if (changeFlags.value.size === 0) {
      changeFlags.value = new Set()
    }
  }

  const hasChanges = computed(() => changeFlags.value.size > 0)

  const isProductChanged = (product: string) => {
    return changeFlags.value.has(product)
  }

  const clearChangeFlags = () => {
    changeFlags.value.clear()
  }

  const trackTradesPriceUpdate = (pair: string, tradeData: TradeData, tickerRef: any) => {
    const currentTicker = tickerRef.value[pair]
    const newPrice = Number(tradeData.p)
    
    if (currentTicker && Number(currentTicker.last) !== newPrice) {
      const updatedTicker = {
        ...currentTicker,
        last: tradeData.p
      }
      
      if (hasSignificantChange(currentTicker, updatedTicker)) {
        tickerRef.value[pair] = updatedTicker
        queueUpdate(pair, updatedTicker)
        return true
      }
    }
    return false
  }

  const getOptimizedUpdateStrategy = () => {
    return {
      hasSignificantChange,
      queueUpdate,
      hasChanges,
      isProductChanged,
      markAsUpdated,
      clearChangeFlags,
      trackTradesPriceUpdate
    }
  }

  return {
    hasSignificantChange,
    queueUpdate,
    hasChanges,
    isProductChanged,
    markAsUpdated,
    clearChangeFlags,
    trackTradesPriceUpdate,
    getOptimizedUpdateStrategy
  }
}