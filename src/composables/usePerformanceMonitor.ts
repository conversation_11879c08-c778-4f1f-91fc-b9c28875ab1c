/**
 * Vue组合式函数：性能监控
 * 提供在Vue组件中使用性能监控的便捷方法
 */

import { ref, onMounted, onBeforeUnmount, onUpdated, watch, computed } from 'vue'
import { createPerformanceMonitor, type PerformanceMonitor } from '~/utils/performanceMonitor'
import { KlineLogger } from '~/config/kline.config'

const logger = KlineLogger.getInstance()

// 全局性能监控实例
let globalMonitor: PerformanceMonitor | null = null

// 性能监控配置
interface UsePerformanceMonitorOptions {
  enableComponentTracking?: boolean
  enableDataFlowTracking?: boolean
  enableNetworkTracking?: boolean
  enableCacheTracking?: boolean
  enableRenderTracking?: boolean
  autoOptimize?: boolean
  memoryThreshold?: number
  renderThreshold?: number
}

// 性能统计
interface PerformanceStats {
  totalRenders: number
  avgRenderTime: number
  memoryUsage: number
  cacheHitRate: number
  networkLatency: number
  dataProcessingTime: number
}

export function usePerformanceMonitor(options: UsePerformanceMonitorOptions = {}) {
  const {
    enableComponentTracking = true,
    enableDataFlowTracking = true,
    enableNetworkTracking = true,
    enableCacheTracking = true,
    enableRenderTracking = true,
    autoOptimize = true,
    memoryThreshold = 80,
    renderThreshold = 50
  } = options

  // 响应式数据
  const isMonitoring = ref(false)
  const performanceStats = ref<PerformanceStats>({
    totalRenders: 0,
    avgRenderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    networkLatency: 0,
    dataProcessingTime: 0
  })
  
  const performanceWarnings = ref<string[]>([])
  const optimizationSuggestions = ref<string[]>([])

  // 组件渲染时间追踪
  const renderStartTime = ref(0)
  const renderTimes = ref<number[]>([])

  // 初始化全局监控器
  const initializeMonitor = () => {
    if (!globalMonitor) {
      globalMonitor = createPerformanceMonitor(
        {
          memoryUsage: memoryThreshold,
          maxRenderTime: renderThreshold
        },
        {
          enableMemoryOptimization: autoOptimize
        }
      )

      // 监听性能警告
      globalMonitor.addObserver('performance_warning', (data: any) => {
        performanceWarnings.value = data.issues
        logger.warn('性能警告', data)
      })

      // 监听内存清理事件
      globalMonitor.addObserver('memory_cleanup', (data: any) => {
        logger.info('内存清理完成', data)
        updatePerformanceStats()
      })

      logger.info('全局性能监控器已初始化')
    }
    return globalMonitor
  }

  // 更新性能统计
  const updatePerformanceStats = () => {
    if (!globalMonitor) return

    const metrics = globalMonitor.getMetrics()
    performanceStats.value = {
      totalRenders: metrics.componentPerformance.reRenderCount,
      avgRenderTime: renderTimes.value.length > 0 ? 
        renderTimes.value.reduce((a, b) => a + b, 0) / renderTimes.value.length : 0,
      memoryUsage: metrics.memoryUsage.percentage,
      cacheHitRate: metrics.cachePerformance.hitRate,
      networkLatency: metrics.networkPerformance.avgLatency,
      dataProcessingTime: metrics.dataFlowPerformance.avgProcessingTime
    }

    // 生成优化建议
    const report = globalMonitor.getPerformanceReport()
    optimizationSuggestions.value = report.recommendations
  }

  // 开始性能追踪
  const startTracking = () => {
    const monitor = initializeMonitor()
    isMonitoring.value = true
    
    if (enableRenderTracking) {
      renderStartTime.value = Date.now()
    }

    logger.debug('组件性能追踪已开始')
    return monitor
  }

  // 停止性能追踪
  const stopTracking = () => {
    if (enableRenderTracking && renderStartTime.value > 0) {
      const renderTime = Date.now() - renderStartTime.value
      renderTimes.value.push(renderTime)
      
      // 保持最近100次渲染记录
      if (renderTimes.value.length > 100) {
        renderTimes.value = renderTimes.value.slice(-50)
      }

      if (globalMonitor) {
        globalMonitor.recordComponentPerformance('unmount', renderTime)
      }
    }

    isMonitoring.value = false
    logger.debug('组件性能追踪已停止')
  }

  // 追踪数据处理性能
  const trackDataProcessing = <T>(
    operation: () => T | Promise<T>,
    operationName?: string
  ): T | Promise<T> => {
    if (!enableDataFlowTracking || !globalMonitor) {
      return operation()
    }

    const startTime = Date.now()
    
    try {
      const result = operation()
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = Date.now()
          globalMonitor!.recordDataProcessing(startTime, endTime)
          updatePerformanceStats()
          
          logger.debug('异步数据处理完成', {
            operation: operationName,
            duration: endTime - startTime
          })
        })
      } else {
        const endTime = Date.now()
        globalMonitor.recordDataProcessing(startTime, endTime)
        updatePerformanceStats()
        
        logger.debug('同步数据处理完成', {
          operation: operationName,
          duration: endTime - startTime
        })
        
        return result
      }
    } catch (error) {
      const endTime = Date.now()
      logger.error('数据处理异常', {
        operation: operationName,
        duration: endTime - startTime,
        error
      })
      throw error
    }
  }

  // 追踪网络请求性能
  const trackNetworkRequest = async <T>(
    request: () => Promise<T>,
    requestName?: string
  ): Promise<T> => {
    if (!enableNetworkTracking || !globalMonitor) {
      return request()
    }

    const startTime = Date.now()
    
    try {
      const result = await request()
      const endTime = Date.now()
      const latency = endTime - startTime
      
      globalMonitor.recordNetworkRequest(latency, true)
      updatePerformanceStats()
      
      logger.debug('网络请求成功', {
        request: requestName,
        latency
      })
      
      return result
    } catch (error) {
      const endTime = Date.now()
      const latency = endTime - startTime
      
      globalMonitor.recordNetworkRequest(latency, false)
      updatePerformanceStats()
      
      logger.error('网络请求失败', {
        request: requestName,
        latency,
        error
      })
      
      throw error
    }
  }

  // 追踪缓存访问性能
  const trackCacheAccess = (hit: boolean, accessTime?: number) => {
    if (!enableCacheTracking || !globalMonitor) return

    globalMonitor.recordCacheAccess(hit, accessTime)
    updatePerformanceStats()
  }

  // 追踪组件渲染性能
  const trackRender = (type: 'mount' | 'update' | 'unmount') => {
    if (!enableRenderTracking || !globalMonitor) return

    const now = Date.now()
    let renderTime = 0

    if (type === 'mount') {
      renderStartTime.value = now
    } else if (renderStartTime.value > 0) {
      renderTime = now - renderStartTime.value
      renderTimes.value.push(renderTime)
      
      if (renderTimes.value.length > 100) {
        renderTimes.value = renderTimes.value.slice(-50)
      }
    }

    globalMonitor.recordComponentPerformance(type, renderTime)
    updatePerformanceStats()
  }

  // 获取性能报告
  const getPerformanceReport = () => {
    if (!globalMonitor) return null
    return globalMonitor.getPerformanceReport()
  }

  // 优化建议
  const getOptimizationSuggestions = () => {
    const suggestions: string[] = []

    if (performanceStats.value.memoryUsage > memoryThreshold) {
      suggestions.push('内存使用率过高，建议清理不必要的数据或增加内存清理频率')
    }

    if (performanceStats.value.avgRenderTime > renderThreshold) {
      suggestions.push('组件渲染时间过长，建议优化渲染逻辑或使用虚拟化技术')
    }

    if (performanceStats.value.cacheHitRate < 70) {
      suggestions.push('缓存命中率偏低，建议调整缓存策略或增加缓存时间')
    }

    if (performanceStats.value.networkLatency > 1000) {
      suggestions.push('网络延迟过高，建议检查网络连接或优化请求策略')
    }

    if (performanceStats.value.dataProcessingTime > 100) {
      suggestions.push('数据处理时间过长，建议优化算法或使用Web Worker')
    }

    return suggestions
  }

  // 自动优化
  const performAutoOptimization = () => {
    if (!autoOptimize || !globalMonitor) return

    const metrics = globalMonitor.getMetrics()
    
    // 内存优化
    if (metrics.memoryUsage.percentage > memoryThreshold) {
      logger.info('执行自动内存优化')
      // 触发内存清理
      globalMonitor.addObserver('memory_cleanup', () => {
        logger.info('自动内存清理完成')
      })
    }

    // 渲染优化
    if (performanceStats.value.avgRenderTime > renderThreshold) {
      logger.info('执行自动渲染优化')
      // 可以在这里实现渲染优化逻辑
      performanceWarnings.value.push('建议减少不必要的组件重渲染')
    }
  }

  // 计算属性
  const isPerformanceGood = computed(() => {
    return performanceStats.value.memoryUsage < memoryThreshold &&
           performanceStats.value.avgRenderTime < renderThreshold &&
           performanceStats.value.cacheHitRate > 70 &&
           performanceStats.value.networkLatency < 1000
  })

  const performanceScore = computed(() => {
    let score = 100

    // 内存使用扣分
    if (performanceStats.value.memoryUsage > 90) score -= 30
    else if (performanceStats.value.memoryUsage > 80) score -= 20
    else if (performanceStats.value.memoryUsage > 70) score -= 10

    // 渲染时间扣分
    if (performanceStats.value.avgRenderTime > 100) score -= 25
    else if (performanceStats.value.avgRenderTime > 50) score -= 15
    else if (performanceStats.value.avgRenderTime > 30) score -= 5

    // 缓存命中率扣分
    if (performanceStats.value.cacheHitRate < 50) score -= 20
    else if (performanceStats.value.cacheHitRate < 70) score -= 10

    // 网络延迟扣分
    if (performanceStats.value.networkLatency > 2000) score -= 15
    else if (performanceStats.value.networkLatency > 1000) score -= 10

    return Math.max(0, score)
  })

  // 生命周期钩子
  onMounted(() => {
    if (enableComponentTracking) {
      startTracking()
      trackRender('mount')
      
      // 定期更新统计
      const interval = setInterval(updatePerformanceStats, 5000)
      
      onBeforeUnmount(() => {
        clearInterval(interval)
        trackRender('unmount')
        stopTracking()
      })
    }
  })

  onUpdated(() => {
    if (enableRenderTracking) {
      trackRender('update')
    }
  })

  // 监听性能统计变化
  watch(performanceStats, (newStats) => {
    if (autoOptimize) {
      performAutoOptimization()
    }
  }, { deep: true })

  return {
    // 响应式数据
    isMonitoring: readonly(isMonitoring),
    performanceStats: readonly(performanceStats),
    performanceWarnings: readonly(performanceWarnings),
    optimizationSuggestions: readonly(optimizationSuggestions),
    
    // 计算属性
    isPerformanceGood,
    performanceScore,
    
    // 方法
    trackDataProcessing,
    trackNetworkRequest,
    trackCacheAccess,
    trackRender,
    getPerformanceReport,
    getOptimizationSuggestions,
    performAutoOptimization,
    updatePerformanceStats,
    
    // 控制方法
    startTracking,
    stopTracking
  }
}

// 全局性能监控钩子
export function useGlobalPerformanceMonitor() {
  const getGlobalMetrics = () => {
    if (!globalMonitor) return null
    return globalMonitor.getMetrics()
  }

  const getGlobalReport = () => {
    if (!globalMonitor) return null
    return globalMonitor.getPerformanceReport()
  }

  const resetGlobalMetrics = () => {
    if (!globalMonitor) return
    globalMonitor.resetMetrics()
  }

  const stopGlobalMonitoring = () => {
    if (!globalMonitor) return
    globalMonitor.stopMonitoring()
    globalMonitor = null
  }

  return {
    getGlobalMetrics,
    getGlobalReport,
    resetGlobalMetrics,
    stopGlobalMonitoring
  }
}