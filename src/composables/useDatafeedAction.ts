import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'
import { createKlineProcessor, type KlineBarData } from '~/utils/klineDataProcessor'
import { KlineLogger } from '~/config/kline.config'

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const requestCache = new Map() // 防抖机制：防止重复请求
  const activeRequests = new Map() // 活跃请求追踪，用于取消
  const REQUEST_TIMEOUT = 30 * 1000 // 请求超时时间：30秒
  const CACHE_DURATION = 10 * 60 * 1000 // 增加到10分钟，支持更长时间的历史数据缓存
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  
  // 数据处理器和质量监控
  const logger = KlineLogger.getInstance()
  const dataQualityMetrics = ref({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    dataGapsDetected: 0,
    dataGapsFilled: 0,
    lastProcessTime: 0
  })
  
  // 为每个周期创建数据处理器实例
  const dataProcessors = new Map<string, any>()
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null
  const updateQueue = new Set<string>()
  const lastPriceUpdates = new Map<string, number>()
  const lastBarTimes = new Map<string, number>()

  // 1w和1M专用请求状态管理
  const weeklyMonthlyRequestStatus = new Map() // 存储1w和1M的请求状态
  const REQUEST_TIMEOUT = 30000 // 30秒超时

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  
  // 获取或创建数据处理器实例
  const getDataProcessor = (resolution: string) => {
    const processorKey = resolutionMap[resolution] || resolution
    if (!dataProcessors.has(processorKey)) {
      dataProcessors.set(processorKey, createKlineProcessor(processorKey))
      logger.debug('创建新的数据处理器', { resolution: processorKey })
    }
    return dataProcessors.get(processorKey)
  }
  
  // 数据质量检查和处理
  const processHistoricalData = (rawData: any[], resolution: string): KlineBarData[] => {
    const processor = getDataProcessor(resolution)
    const startTime = Date.now()
    
    try {
      logger.debug('开始历史数据处理', { 
        原始数据量: rawData.length, 
        周期: resolutionMap[resolution] 
      })
      
      // 数据标准化
      const normalizedData = processor.normalizeData(rawData.map(item => {
        const time = Number(item[0])
        const open = Math.abs(Number(item[1]))
        const high = Math.abs(Number(item[2]))
        const low = Math.abs(Number(item[3]))
        const close = Math.abs(Number(item[4]))
        const volume = Math.abs(Number(item[5]))
        return { time, open, high, low, close, volume }
      }))
      
      // 数据质量检查
      const qualityResult = processor.validateDataQuality(normalizedData)
      let processedData = normalizedData
      
      if (!qualityResult.isValid) {
        logger.warn('专业版历史数据质量检查失败', {
          errors: qualityResult.errors,
          warnings: qualityResult.warnings
        })
        
        // 尝试修复数据间隙
        const gapsBeforeFill = processor.detectDataGaps(normalizedData)
        if (gapsBeforeFill.length > 0) {
          dataQualityMetrics.value.dataGapsDetected += gapsBeforeFill.length
          processedData = processor.fillDataGaps(normalizedData)
          
          const gapsAfterFill = processor.detectDataGaps(processedData)
          const filledGaps = gapsBeforeFill.length - gapsAfterFill.length
          dataQualityMetrics.value.dataGapsFilled += filledGaps
          
          logger.info('专业版数据间隙填充完成', {
            原始间隙数: gapsBeforeFill.length,
            填充后间隙数: gapsAfterFill.length,
            成功填充: filledGaps
          })
        }
      }
      
      dataQualityMetrics.value.lastProcessTime = Date.now() - startTime
      logger.debug('专业版历史数据处理完成', {
        原始数据量: rawData.length,
        处理后数据量: processedData.length,
        处理耗时: dataQualityMetrics.value.lastProcessTime
      })
      
      return processedData
      
    } catch (error) {
      logger.error('专业版历史数据处理失败', error)
      // 降级处理：返回基本格式化的数据
      return rawData.map(item => ({
        time: Number(item[0]),
        open: Math.abs(Number(item[1])),
        high: Math.abs(Number(item[2])),
        low: Math.abs(Number(item[3])),
        close: Math.abs(Number(item[4])),
        volume: Math.abs(Number(item[5]))
      })).sort((a, b) => a.time - b.time)
    }
  }
  let key = ''

  // 优化后的1w和1M处理逻辑
  async function handleWeeklyMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams
    const requestKey = `${symbolInfo.fullName}-${resolution}`

    // 检查是否已经有请求在进行或已完成
    const existingStatus = weeklyMonthlyRequestStatus.get(requestKey)
    if (existingStatus) {
      if (existingStatus.status === 'completed' && existingStatus.data) {
        // 已完成的请求，直接返回缓存数据
        logger.debug(`${resolution}数据已缓存，直接返回`, { symbol: symbolInfo.fullName })
        onHistoryCallback(existingStatus.data, { noData: false })
        return true
      } else if (existingStatus.status === 'pending') {
        // 请求正在进行中，等待完成
        logger.debug(`${resolution}请求正在进行中，等待完成`, { symbol: symbolInfo.fullName })
        return new Promise((resolve) => {
          const checkInterval = setInterval(() => {
            const currentStatus = weeklyMonthlyRequestStatus.get(requestKey)
            if (currentStatus && currentStatus.status === 'completed' && currentStatus.data) {
              clearInterval(checkInterval)
              onHistoryCallback(currentStatus.data, { noData: false })
              resolve(true)
            }
          }, 100)

          // 10秒超时
          setTimeout(() => {
            clearInterval(checkInterval)
            resolve(false)
          }, 10000)
        })
      }
    }

    // 标记请求开始
    weeklyMonthlyRequestStatus.set(requestKey, { status: 'pending', timestamp: Date.now() })
    return false // 让调用方继续执行API请求
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]
    key = `${symbolInfo.fullName}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    // 智能缓存清理：只在真正的周期切换时才清理冲突缓存
    const currentResolution = resolutionMap[resolution]
    const isResolutionChange = firstDataRequest && interval.value && interval.value !== currentResolution
    
    if (isResolutionChange) {
      // 清理所有与当前symbol相关的缓存，但保留新周期的缓存
      const symbolPrefix = `${symbolInfo.fullName}-`
      const newResolutionPrefix = `${symbolInfo.fullName}-${currentResolution}-`
      
      const keysToDelete = []
      dataCache.forEach((value, key) => {
        // 清理所有同一symbol但不同周期的缓存
        if (key.startsWith(symbolPrefix) && !key.startsWith(newResolutionPrefix)) {
          keysToDelete.push(key)
        }
      })
      
      if (keysToDelete.length > 0) {
        keysToDelete.forEach(key => dataCache.delete(key))
      }
      
      // 同步清理请求缓存 - 使用相同的格式
      const requestKeysToDelete = []
      requestCache.forEach((timestamp, key) => {
        if (key.startsWith(symbolPrefix) && !key.includes(`-${currentResolution}-`)) {
          requestKeysToDelete.push(key)
        }
      })
      
      if (requestKeysToDelete.length > 0) {
        requestKeysToDelete.forEach(key => requestCache.delete(key))
      }
    }

    // 1w和1M的统一处理
    if (resolution === '1M' || resolution === '1W') {
      const handled = await handleWeeklyMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    // 确保interval.value始终与当前周期保持同步
    if (!interval.value || isResolutionChange) {
      interval.value = currentResolution
    }
    
    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = (selective = false) => {
    if (selective) {
      // 选择性清理：只清理可能冲突的缓存，保留有用的数据
      dataCache.clear()
      lastCompleteBar.value = {}
      
      // 清理过期请求但不取消活跃请求
      cleanupExpiredRequests()
    } else {
      // 全量清理：取消所有请求并清理所有数据
      cancelAllActiveRequests()
      
      dataCache.clear()
      lastCompleteBar.value = {}
      lastPriceUpdates.clear()
      lastBarTimes.clear()
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}
    }
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    
    // 如果设置为true，清理过期的请求缓存以防止阻塞
    if (force) {
      cleanupExpiredRequests()
    }
  }
  // 请求清理机制：防止requestCache永久阻塞
  const cleanupExpiredRequests = () => {
    const now = Date.now()
    const expiredKeys = []
    
    // 清理过期的请求标记
    requestCache.forEach((timestamp, key) => {
      if (now - timestamp > REQUEST_TIMEOUT) {
        expiredKeys.push(key)
      }
    })
    expiredKeys.forEach(key => {
      requestCache.delete(key)
      // 同时清理对应的活跃请求
      if (activeRequests.has(key)) {
        const controller = activeRequests.get(key)
        if (controller && !controller.signal.aborted) {
          controller.abort('Request timeout')
        }
        activeRequests.delete(key)
      }
    })
  }

  // 取消所有活跃请求
  const cancelAllActiveRequests = () => {
    activeRequests.forEach((controller, key) => {
      if (controller && !controller.signal.aborted) {
        controller.abort('Component cleanup')
      }
    })
    activeRequests.clear()
    requestCache.clear()
  }

  // 移除定期清理定时器，改为手动清理
  // setInterval(cleanupExpiredRequests, REQUEST_TIMEOUT / 2)

  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    // 手动清理过期请求（移除定时器）
    cleanupExpiredRequests()

    // 1w和1M使用更长的缓存时间，避免重复请求
    const isWeeklyOrMonthly = resolution === '1W' || resolution === '1M'
    const cacheTimeout = isWeeklyOrMonthly ? 24 * 60 * 60 * 1000 : CACHE_DURATION // 1w/1M: 24小时，其他: 10分钟

    // 优化缓存策略：使用统一的缓存key格式
    const timeRangeKey = firstDataRequest ? 'initial' : Math.floor(from / (24 * 60 * 60 * 1000))
    const cacheKey = `${symbol}-${resolutionMap[resolution]}-${timeRangeKey}`

    // 1w和1M的特殊处理：只允许一次请求
    if (isWeeklyOrMonthly && firstDataRequest) {
      const weeklyMonthlyKey = `${symbol}-${resolution}`
      const existingStatus = weeklyMonthlyRequestStatus.get(weeklyMonthlyKey)
      if (existingStatus && existingStatus.status === 'pending') {
        logger.debug(`${resolution}请求已在进行中，跳过重复请求`, { symbol })
        return
      }
    }

    // 防抖机制：检查是否有相同请求正在进行
    const requestKey = `${symbol}-${resolutionMap[resolution]}-${from}-${to}-${firstDataRequest}`
    if (requestCache.has(requestKey)) {
      return // 相同请求正在进行，忽略本次请求
    }

    const cachedData = dataCache.get(cacheKey)
    
    // 简化缓存使用条件，让TradingView主导缓存决策
    const shouldUseCache = cachedData && 
                          (Date.now() - cachedData.timestamp < cacheTimeout) && 
                          !forceRefresh.value &&
                          cachedData.data.length > 0 // 确保有有效数据
    
    if (shouldUseCache) {
      // 简化noData判断逻辑，使用更合理的阈值
      const minDataThreshold = firstDataRequest ? 30 : 10  // 降低阈值，避免过度要求
      const noMoreData = cachedData.data.length < minDataThreshold
      
      // 在成功使用缓存后重置强制刷新标志
      if (forceRefresh.value) {
        forceRefresh.value = false
      }
      
      onHistoryCallback(cachedData.data, { noData: noMoreData })
      return
    }

    try {
      // 创建AbortController用于请求取消
      const controller = new AbortController()
      
      // 标记请求开始，记录时间戳和控制器
      requestCache.set(requestKey, Date.now())
      activeRequests.set(requestKey, controller)
      
      const now = Date.now()
      dataQualityMetrics.value.totalRequests++
      
      // 优化请求策略：1w和1M使用更大的请求量，确保一次获取足够数据
      const requestLimit = firstDataRequest ?
        (isWeeklyOrMonthly ? Math.max(countBack > 2000 ? 2000 : countBack, 500) : Math.max(countBack > 1000 ? 1000 : countBack, 300)) :
        (isWeeklyOrMonthly ? Math.min(countBack, 200) : Math.min(countBack, 300))  // 历史请求限制

      // 恢复原始的时间逻辑，确保数据正确加载
      let beforeTime
      if (firstDataRequest) {
        // 首次请求：使用当前时间
        beforeTime = now
      } else {
        // 历史数据请求：使用preObj或to参数
        beforeTime = preObj.value && (preObj.value as any).time ? (preObj.value as any).time : to
      }

      // 检查请求是否已被取消
      if (controller.signal.aborted) {
        return
      }
      
      logger.debug('专业版K线数据请求开始', {
        symbol,
        resolution: resolutionMap[resolution],
        requestLimit,
        beforeTime: new Date(beforeTime).toISOString(),
        firstDataRequest
      })

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: beforeTime,
        limit: requestLimit,
        origin:1,
      })
      
      // 再次检查请求是否在API调用期间被取消
      if (controller.signal.aborted) {
        return
      }

      if (data) {
        dataQualityMetrics.value.successfulRequests++
        
        // 使用增强的数据处理功能
        const formattedData = processHistoricalData(data.e, resolution)

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
          
          logger.info('专业版K线数据请求成功', {
            symbol,
            resolution: resolutionMap[resolution],
            数据量: formattedData.length,
            时间范围: {
              start: new Date(formattedData[0].time).toISOString(),
              end: new Date(formattedData[formattedData.length - 1].time).toISOString()
            },
            数据质量指标: {
              总请求数: dataQualityMetrics.value.totalRequests,
              成功请求数: dataQualityMetrics.value.successfulRequests,
              检测到的间隙: dataQualityMetrics.value.dataGapsDetected,
              填充的间隙: dataQualityMetrics.value.dataGapsFilled
            }
          })
        }

        // 1w和1M的特殊处理：标记请求完成并缓存数据
        if (isWeeklyOrMonthly && firstDataRequest) {
          const weeklyMonthlyKey = `${symbol}-${resolution}`
          weeklyMonthlyRequestStatus.set(weeklyMonthlyKey, {
            status: 'completed',
            data: formattedData,
            timestamp: Date.now()
          })
          logger.debug(`${resolution}数据请求完成并缓存`, { symbol, dataLength: formattedData.length })
        }

        // 1w和1M总是返回noData=true，避免TradingView请求更多数据
        const noMoreData = isWeeklyOrMonthly ? true : (formattedData.length === 0 || formattedData.length < (firstDataRequest ? 50 : 10))
        onHistoryCallback(formattedData, { noData: noMoreData })
      } else {
        logger.warn('专业版API返回数据为空', { symbol, resolution: resolutionMap[resolution] })
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      dataQualityMetrics.value.failedRequests++
      
      // 检查是否是取消错误
      if (error.name === 'AbortError' || error.message?.includes('aborted') || error.message?.includes('timeout')) {
        return // 取消的请求不调用错误回调
      }
      
      logger.error('专业版K线数据请求失败', {
        symbol,
        resolution: resolutionMap[resolution],
        error: error.message,
        质量指标: dataQualityMetrics.value
      })
      
      onErrorCallback(error)
    } finally {
      // 确保请求标记和活跃请求总是被清理
      try {
        requestCache.delete(requestKey)
        activeRequests.delete(requestKey)
      } catch (cleanupError) {
      }
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      try {
        // 使用无缝衔接算法处理1M数据
        const processor = getDataProcessor('1M')
        const realtimePrice = safeNumber(last)
        const realtimeVolume = safeNumber(val2.volume)
        const realtimeTime = Number(val2.time)

        // 构建实时数据
        const realtimeData: Partial<KlineBarData> = {
          time: realtimeTime,
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: realtimePrice,
          volume: realtimeVolume
        }

        // 如果有历史数据，进行无缝衔接
        if (klineList.value && klineList.value.length > 0) {
          const mergedData = processor.seamlessConnect(
            klineList.value,
            realtimePrice,
            realtimeVolume,
            realtimeTime
          )

          if (mergedData.length > 0) {
            const resultVal = mergedData[mergedData.length - 1]
            
            const monthlyStateKey = `${pair.value}_#_1M`
            lastCompleteBar.value[monthlyStateKey] = {
              open: resultVal.open,
              high: resultVal.high,
              low: resultVal.low,
              volume: resultVal.volume
            }

            const currentTime = resultVal.time || Date.now()
            const lastTime = lastBarTimes.get(monthlyStateKey) || 0
            if (currentTime > lastTime) {
              lastBarTimes.set(monthlyStateKey, currentTime)
              monthlySubscription.listen(resultVal)
              
              logger.debug('专业版1M实时数据无缝衔接成功', {
                time: new Date(currentTime).toISOString(),
                price: resultVal.close,
                volume: resultVal.volume
              })
            }
            return true
          }
        }

        // 降级处理：直接构建结果数据
        const resultVal = {
          time: realtimeTime,
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: realtimePrice,
          volume: realtimeVolume
        }

        const monthlyStateKey = `${pair.value}_#_1M`
        lastCompleteBar.value[monthlyStateKey] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }

        const currentTime = resultVal.time || Date.now()
        const lastTime = lastBarTimes.get(monthlyStateKey) || 0
        if (currentTime > lastTime) {
          lastBarTimes.set(monthlyStateKey, currentTime)
          monthlySubscription.listen(resultVal)
        }
        return true
        
      } catch (error) {
        logger.error('专业版1M实时数据处理失败', error)
        // 降级到原有处理逻辑
        const resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }

        const monthlyStateKey = `${pair.value}_#_1M`
        lastCompleteBar.value[monthlyStateKey] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }

        const currentTime = resultVal.time || Date.now()
        const lastTime = lastBarTimes.get(monthlyStateKey) || 0
        if (currentTime > lastTime) {
          lastBarTimes.set(monthlyStateKey, currentTime)
          monthlySubscription.listen(resultVal)
        }
        return true
      }
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      try {
        // 使用数据处理器进行无缝衔接
        const processor = getDataProcessor(subMap[key].resolution)
        const realtimePrice = safeNumber(last)
        
        let resultVal

        if (val2 && val2.currentPair && val2.currentPeriod &&
            formatSymbol(subMap[key].symbol) === val2.currentPair &&
            interval.value === val2.currentPeriod) {
          
          // 完整的实时数据
          const realtimeData: Partial<KlineBarData> = {
            time: Number(val2.time),
            open: safeNumber(val2.open),
            high: safeNumber(val2.high),
            low: safeNumber(val2.low),
            close: realtimePrice,
            volume: safeNumber(val2.volume)
          }
          
          // 如果有历史缓存数据，尝试无缝衔接
          const cacheKey = `${pair.value}-${interval.value}-initial`
          const cachedData = dataCache.get(cacheKey)
          
          if (cachedData && cachedData.data && cachedData.data.length > 0) {
            const mergedData = processor.seamlessConnect(
              cachedData.data,
              realtimePrice,
              realtimeData.volume,
              realtimeData.time
            )
            
            if (mergedData.length > 0) {
              resultVal = mergedData[mergedData.length - 1]
              
              logger.debug('专业版实时数据无缝衔接成功', {
                pair: pair.value,
                resolution: interval.value,
                time: new Date(resultVal.time).toISOString(),
                price: resultVal.close
              })
            } else {
              resultVal = realtimeData as KlineBarData
            }
          } else {
            resultVal = realtimeData as KlineBarData
          }
          
          lastCompleteBar.value[key] = {
            open: resultVal.open,
            high: resultVal.high,
            low: resultVal.low,
            volume: resultVal.volume
          }
          
        } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
          const baseBar = lastCompleteBar.value[key] || {}
          const currentClose = realtimePrice
          resultVal = {
            time: Number(val2.time) || Date.now(),
            open: safeNumber(val2.open, baseBar.open || currentClose),
            high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
            low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
            close: currentClose,
            volume: safeNumber(val2.volume, baseBar.volume)
          }
        } else if (lastCompleteBar.value[key]) {
          const baseBar = lastCompleteBar.value[key]
          const currentClose = realtimePrice
          resultVal = {
            time: Date.now(),
            open: baseBar.open,
            high: Math.max(baseBar.high, currentClose),
            low: Math.min(baseBar.low, currentClose),
            close: currentClose,
            volume: baseBar.volume
          }
        } else {
          const currentClose = realtimePrice
          resultVal = {
            time: Date.now(),
            open: currentClose,
            high: currentClose,
            low: currentClose,
            close: currentClose,
            volume: 0
          }
        }

        const currentUpdate = val1[pair.value] && val1[pair.value]._lastUpdate
        const lastUpdate = lastPriceUpdates.get(pair.value)
        const isPriceUpdate = currentUpdate && currentUpdate !== lastUpdate
        
        // 智能更新逻辑：优先处理实时价格更新，使用时间边界检测
        if (isPriceUpdate) {
          lastPriceUpdates.set(pair.value, currentUpdate)
          if (subMap[key] && subMap[key].listen) {
            // 时间边界检测确保更新的合理性
            const timeBoundary = processor.detectTimeBoundary(
              resultVal.time,
              lastBarTimes.get(key) || null,
              Date.now()
            )
            
            if (timeBoundary.shouldUpdate || timeBoundary.isNewBar) {
              subMap[key].listen(resultVal)
              lastBarTimes.set(key, resultVal.time || Date.now())
              
              logger.debug('专业版实时价格更新', {
                pair: pair.value,
                resolution: interval.value,
                price: resultVal.close,
                reason: timeBoundary.reason
              })
            }
          }
        } else if (subMap[key] && subMap[key].listen) {
          // 对于非实时价格更新，仍然发送更新以保持K线图数据完整性
          const currentTime = resultVal.time || Date.now()
          const lastTime = lastBarTimes.get(key) || 0
          
          // 时间验证：确保数据更新的合理性
          if (currentTime >= lastTime) {
            subMap[key].listen(resultVal)
            lastBarTimes.set(key, currentTime)
          }
        }
        
      } catch (error) {
        logger.error('专业版实时数据处理失败', {
          pair: pair.value,
          resolution: interval.value,
          error: error.message
        })
        
        // 降级处理：使用原有逻辑
        let resultVal = {
          time: Date.now(),
          open: safeNumber(last),
          high: safeNumber(last),
          low: safeNumber(last),
          close: safeNumber(last),
          volume: 0
        }

        const currentUpdate = val1[pair.value] && val1[pair.value]._lastUpdate
        const lastUpdate = lastPriceUpdates.get(pair.value)
        const isPriceUpdate = currentUpdate && currentUpdate !== lastUpdate
        
        if (isPriceUpdate) {
          lastPriceUpdates.set(pair.value, currentUpdate)
          if (subMap[key] && subMap[key].listen) {
            subMap[key].listen(resultVal)
            lastBarTimes.set(key, resultVal.time || Date.now())
          }
        }
      }
    }
  }, { 
    deep: true, 
    immediate: true,
    flush: 'sync'
  })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    const currentKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key !== currentKey) {
        delete lastCompleteBar.value[key]
      }
    })

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          onRealtimeCallback(newPriceData)
        } catch (error) {
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  // 获取数据质量监控指标
  const getDataQualityMetrics = () => {
    return {
      ...dataQualityMetrics.value,
      activeProcessors: dataProcessors.size,
      cacheEntries: dataCache.size,
      activeRequests: activeRequests.size
    }
  }
  
  // 重置数据质量监控指标
  const resetDataQualityMetrics = () => {
    dataQualityMetrics.value = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      dataGapsDetected: 0,
      dataGapsFilled: 0,
      lastProcessTime: 0
    }
    logger.info('专业版数据质量监控指标已重置')
  }
  
  // 增强版缓存清理
  const enhancedClearCache = (selective = false) => {
    if (selective) {
      // 选择性清理：只清理可能冲突的缓存，保留有用的数据
      dataCache.clear()
      lastCompleteBar.value = {}

      // 清理过期请求但不取消活跃请求
      cleanupExpiredRequests()
    } else {
      // 全量清理：取消所有请求并清理所有数据
      cancelAllActiveRequests()

      dataCache.clear()
      lastCompleteBar.value = {}
      lastPriceUpdates.clear()
      lastBarTimes.clear()
      dataProcessors.clear() // 清理数据处理器
      weeklyMonthlyRequestStatus.clear() // 清理1w和1M请求状态
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}

      // 重置数据质量监控
      resetDataQualityMetrics()
    }

    logger.info('专业版缓存清理完成', { selective })
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: 'etc/UTC', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
      
      logger.debug('专业版取消订阅', { subscriberUID })
    },
    clearCache: enhancedClearCache,  // 使用增强版缓存清理
    setForceRefresh,
    cancelAllActiveRequests,
    
    // 新增：数据质量监控接口
    getDataQualityMetrics,
    resetDataQualityMetrics,
    
    // 新增：数据处理器管理接口  
    getDataProcessor,
    
    // 新增：日志器访问接口
    logger: logger
  }
}