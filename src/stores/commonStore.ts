import { defineStore } from 'pinia'
import { getErrors, listMainCoins, getPairList, getCurrencyRateData } from '~/api/public'
import { assetsByCoin, assetByCoinList } from '~/api/tf'
import { getTradesList, getKlinesApi } from '~/api/order'
import { getPairSetting, getLandingPairsApi, getAddDownLink } from '~/api/public'
import { useI18n } from "vue-i18n"
import { cookies } from '~/utils/cookies'
import { socket, socketAllTicker } from '~/utils'
import SocketClass from '~/utils/socket.ts'
import { nextTick } from 'vue'
import { createKlineProcessor, validateKlineData, type KlineBarData } from '~/utils/klineDataProcessor'
import { KlineLogger } from '~/config/kline.config'

let localPairInfo = {}
if (process.client) {
  localPairInfo = JSON.parse(localStorage.getItem('pairInfo') || '{}')
}
export const commonStore = defineStore('publicCommon', () => {
  const errorMessages = ref({})
  const coinList = ref([])
  const currencyRate = ref({})
  const exchangeRate = ref({
    rate: 'USD',
    symbol: '$'
  })
  const pair = ref('')
  const depthsStore = ref({})
  const coinAssetListObj = ref({})
  const CoinAssetObj = ref({})
  const mainAssetObj = ref({})
  const tradeAssetObj = ref({})
  const posMapObj = ref({})
  const klineList = ref([])
  const klineTicker = ref({})
  const allPairList = ref([])
  const dealsObj = ref({})
  const allAsset = ref({})
  const marketsObj = ref({})
  const COLLATERALSymbol = ref({})
  const assetAllCoinMap = ref({})
  const ticker = ref({})
  const isChangeOrder = ref(true)
  const isChangeFutureOrder = ref(false)
  const isChangePosition = ref(false)
  const orderChangeObj = ref({})
  const isHideAssets = ref(false)
  const pairInfo = ref(localPairInfo)
  const priceScale = ref(0)
  const quantityScale = ref(0)
  const isPairDetail = ref(false)
  const landingPairs = ref({})
  const tradeArr = ref([])
  const downLoadInfo = ref({})
  const currentLang = useI18n().locale.value
  
  
  // 数据质量监控和缓存优化
  const logger = KlineLogger.getInstance()
  const globalDataQuality = ref({
    totalSocketConnections: 0,
    successfulSocketConnections: 0,
    failedSocketConnections: 0,
    websocketErrors: 0,
    klineDataErrors: 0,
    priceDataErrors: 0,
    lastHealthCheck: Date.now(),
    connectionUptime: 0,
    averageLatency: 0,
    dataLossEvents: 0,
    recoveryEvents: 0
  })
  
  // 增强版缓存管理 - 支持命名空间隔离
  const cacheManager = ref({
    // 基础版缓存
    basicKlineCache: new Map(),
    basicTickerCache: new Map(),
    basicDepthCache: new Map(),
    
    // 专业版缓存
    professionalKlineCache: new Map(),
    professionalTickerCache: new Map(),
    professionalDepthCache: new Map(),
    
    // 共享缓存
    sharedCache: new Map(),
    
    lastCleanupTime: Date.now(),
    cacheHitRate: 0,
    cacheMissRate: 0,
    totalCacheRequests: 0,
    cacheMemoryUsage: 0,
    
    // 缓存冲突检测
    conflictDetection: {
      enabled: true,
      conflicts: new Map(),
      lastConflictCheck: Date.now()
    }
  })
  
  // 错误恢复机制
  const errorRecovery = ref({
    retryAttempts: new Map(),
    maxRetries: 3,
    retryDelay: 1000,
    isRecovering: false,
    lastRecoveryTime: 0,
    criticalErrors: []
  })
  
  // 数据处理器管理
  const dataProcessors = new Map<string, any>()
  
  // 获取或创建数据处理器
  const getDataProcessor = (resolution: string) => {
    if (!dataProcessors.has(resolution)) {
      dataProcessors.set(resolution, createKlineProcessor(resolution))
      logger.debug('创建全局数据处理器', { resolution })
    }
    return dataProcessors.get(resolution)
  }
  
  // 数据质量监控函数
  const updateDataQuality = (type: string, isSuccess: boolean = true) => {
    const metrics = globalDataQuality.value
    
    switch (type) {
      case 'socket_connection':
        metrics.totalSocketConnections++
        if (isSuccess) {
          metrics.successfulSocketConnections++
        } else {
          metrics.failedSocketConnections++
        }
        break
      case 'websocket_error':
        metrics.websocketErrors++
        break
      case 'kline_data_error':
        metrics.klineDataErrors++
        break
      case 'price_data_error':
        metrics.priceDataErrors++
        break
      case 'data_loss':
        metrics.dataLossEvents++
        break
      case 'recovery':
        metrics.recoveryEvents++
        errorRecovery.value.lastRecoveryTime = Date.now()
        break
    }
    
    logger.debug('数据质量指标更新', { type, isSuccess, currentMetrics: metrics })
  }
  
  // 缓存管理函数 - 支持命名空间
  const manageCacheEntry = (cacheType: string, key: string, data: any, ttl: number = 300000, namespace: string = 'shared') => {
    const cacheKey = `${namespace}${cacheType.charAt(0).toUpperCase() + cacheType.slice(1)}Cache`
    const cache = cacheManager.value[cacheKey]
    
    if (cache instanceof Map) {
      // 冲突检测
      if (cacheManager.value.conflictDetection.enabled) {
        detectCacheConflict(cacheType, key, namespace)
      }
      
      cache.set(key, {
        data,
        timestamp: Date.now(),
        ttl,
        accessCount: 0,
        namespace,
        dataHash: generateDataHash(data)
      })
      
      // 更新缓存统计
      cacheManager.value.totalCacheRequests++
      
      logger.debug('缓存条目已创建', { 
        cacheType, 
        key, 
        namespace,
        dataSize: JSON.stringify(data).length 
      })
    } else {
      logger.warn('无效的缓存类型或命名空间', { cacheType, namespace, cacheKey })
    }
  }
  
  const getCacheEntry = (cacheType: string, key: string, namespace: string = 'shared') => {
    const cacheKey = `${namespace}${cacheType.charAt(0).toUpperCase() + cacheType.slice(1)}Cache`
    const cache = cacheManager.value[cacheKey]
    
    if (cache instanceof Map && cache.has(key)) {
      const entry = cache.get(key)
      const now = Date.now()
      
      // 检查TTL
      if (now - entry.timestamp > entry.ttl) {
        cache.delete(key)
        cacheManager.value.cacheMissRate++
        return null
      }
      
      entry.accessCount++
      cacheManager.value.cacheHitRate++
      
      logger.debug('缓存命中', { cacheType, key, namespace })
      return entry.data
    }
    
    cacheManager.value.cacheMissRate++
    logger.debug('缓存未命中', { cacheType, key, namespace })
    return null
  }
  
  // 生成数据哈希用于冲突检测
  const generateDataHash = (data: any): string => {
    try {
      const str = JSON.stringify(data)
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
      }
      return hash.toString(16)
    } catch (error) {
      return 'hash_error'
    }
  }
  
  // 缓存冲突检测
  const detectCacheConflict = (cacheType: string, key: string, namespace: string) => {
    const conflictKey = `${cacheType}_${key}`
    const conflicts = cacheManager.value.conflictDetection.conflicts
    
    if (conflicts.has(conflictKey)) {
      const existing = conflicts.get(conflictKey)
      if (existing.namespace !== namespace) {
        logger.warn('检测到缓存冲突', {
          cacheType,
          key,
          existingNamespace: existing.namespace,
          newNamespace: namespace,
          conflictTime: Date.now()
        })
        
        // 记录冲突
        existing.conflictCount = (existing.conflictCount || 0) + 1
        existing.lastConflict = Date.now()
      }
    } else {
      conflicts.set(conflictKey, {
        namespace,
        firstSeen: Date.now(),
        conflictCount: 0
      })
    }
  }
  
  // 清理过期缓存 - 支持命名空间
  const cleanupExpiredCache = (targetNamespace?: string) => {
    const now = Date.now()
    const namespaces = ['basic', 'professional', 'shared']
    const cacheTypes = ['kline', 'ticker', 'depth']
    let totalCleared = 0
    
    namespaces.forEach(namespace => {
      // 如果指定了目标命名空间，只清理该命名空间
      if (targetNamespace && namespace !== targetNamespace) {
        return
      }
      
      cacheTypes.forEach(cacheType => {
        const cacheKey = `${namespace}${cacheType.charAt(0).toUpperCase() + cacheType.slice(1)}Cache`
        const cache = cacheManager.value[cacheKey]
        
        if (cache instanceof Map) {
          const beforeSize = cache.size
          for (const [key, entry] of cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
              cache.delete(key)
              totalCleared++
            }
          }
          
          if (cache.size < beforeSize) {
            logger.debug('命名空间缓存清理', {
              namespace,
              cacheType,
              beforeSize,
              afterSize: cache.size,
              cleared: beforeSize - cache.size
            })
          }
        }
      })
    })
    
    cacheManager.value.lastCleanupTime = now
    if (totalCleared > 0) {
      logger.info('缓存清理完成', { 
        clearedEntries: totalCleared,
        targetNamespace: targetNamespace || 'all'
      })
    }
  }
  
  // 清理特定命名空间的缓存
  const clearNamespaceCache = (namespace: string) => {
    const cacheTypes = ['kline', 'ticker', 'depth']
    let totalCleared = 0
    
    cacheTypes.forEach(cacheType => {
      const cacheKey = `${namespace}${cacheType.charAt(0).toUpperCase() + cacheType.slice(1)}Cache`
      const cache = cacheManager.value[cacheKey]
      
      if (cache instanceof Map) {
        totalCleared += cache.size
        cache.clear()
      }
    })
    
    logger.info('命名空间缓存已清空', { namespace, clearedEntries: totalCleared })
    return totalCleared
  }
  
  // 获取缓存冲突报告
  const getCacheConflictReport = () => {
    const conflicts = cacheManager.value.conflictDetection.conflicts
    const report = {
      totalConflicts: 0,
      activeConflicts: 0,
      conflictDetails: []
    }
    
    for (const [key, data] of conflicts.entries()) {
      if (data.conflictCount > 0) {
        report.totalConflicts++
        if (Date.now() - data.lastConflict < 300000) { // 5分钟内的冲突
          report.activeConflicts++
        }
        
        report.conflictDetails.push({
          key,
          namespace: data.namespace,
          conflictCount: data.conflictCount,
          firstSeen: new Date(data.firstSeen).toISOString(),
          lastConflict: data.lastConflict ? new Date(data.lastConflict).toISOString() : null
        })
      }
    }
    
    return report
  }
  
  // 错误恢复机制
  const attemptRecovery = async (errorType: string, recoveryFunction: Function, ...args: any[]) => {
    const retryKey = `${errorType}_${args.join('_')}`
    const currentRetries = errorRecovery.value.retryAttempts.get(retryKey) || 0
    
    if (currentRetries >= errorRecovery.value.maxRetries) {
      logger.error('错误恢复达到最大重试次数', { errorType, retryKey, maxRetries: errorRecovery.value.maxRetries })
      errorRecovery.value.criticalErrors.push({
        type: errorType,
        timestamp: Date.now(),
        retryKey,
        finalError: '超过最大重试次数'
      })
      return false
    }
    
    try {
      errorRecovery.value.isRecovering = true
      errorRecovery.value.retryAttempts.set(retryKey, currentRetries + 1)
      
      // 等待重试延迟
      await new Promise(resolve => setTimeout(resolve, errorRecovery.value.retryDelay * (currentRetries + 1)))
      
      const result = await recoveryFunction(...args)
      
      // 恢复成功，清理重试记录
      errorRecovery.value.retryAttempts.delete(retryKey)
      updateDataQuality('recovery', true)
      
      logger.info('错误恢复成功', { errorType, retryKey, attempts: currentRetries + 1 })
      return result
      
    } catch (error) {
      logger.warn('错误恢复失败', { errorType, retryKey, attempt: currentRetries + 1, error: error.message })
      throw error
    } finally {
      errorRecovery.value.isRecovering = false
    }
  }
  
  // 健康检查
  const performHealthCheck = () => {
    const now = Date.now()
    const metrics = globalDataQuality.value
    
    // 计算连接正常运行时间
    if (metrics.successfulSocketConnections > 0) {
      metrics.connectionUptime = now - metrics.lastHealthCheck
    }
    
    // 计算缓存命中率
    const totalCacheOps = cacheManager.value.cacheHitRate + cacheManager.value.cacheMissRate
    if (totalCacheOps > 0) {
      const hitRate = (cacheManager.value.cacheHitRate / totalCacheOps) * 100
      logger.debug('缓存性能统计', { 
        hitRate: `${hitRate.toFixed(2)}%`, 
        totalOperations: totalCacheOps 
      })
    }
    
    // 清理过期缓存
    if (now - cacheManager.value.lastCleanupTime > 300000) { // 5分钟清理一次
      cleanupExpiredCache()
    }
    
    metrics.lastHealthCheck = now
    
    logger.debug('健康检查完成', {
      totalConnections: metrics.totalSocketConnections,
      successRate: metrics.totalSocketConnections > 0 ? 
        (metrics.successfulSocketConnections / metrics.totalSocketConnections * 100).toFixed(2) + '%' : '0%',
      errorCounts: {
        websocket: metrics.websocketErrors,
        klineData: metrics.klineDataErrors,
        priceData: metrics.priceDataErrors
      }
    })
  }
  
  // 定期健康检查
  if (process.client) {
    setInterval(performHealthCheck, 60000) // 每分钟检查一次
  }
  
  const changePair = (p) => {
    const router = useRouter()
    const nuxtApp = useNuxtApp()
    const lang = nuxtApp.$i18n.locale.value
    if (p.includes('_SWAP')) {
      pair.value = p
      nextTick(() => {
        window.history.replaceState({}, null, `/${lang}/future/${p}`)
      })
      router.currentRoute.value.params.pair = p
    } else {
      router.push(`/${lang}/exchange/${p}`)
    }
  }
  const getDownLoadInfo = async() => {
    const { data } = await getAddDownLink()
    if (data) {
      const arr = data.filter((item) => {
        return item.device_type * 1 === 3 || item.device_type * 1 === 4 // 3是IOS 4是安卓
      })
      let obj = {}
      arr.forEach((item) => {
        obj[item.device_type * 1] = item
      })
      downLoadInfo.value = obj
    }
  }
  const getLandingPairs = async() => {
    const { data } = await getLandingPairsApi()
    if (data) {
      let obj = {}
      data.forEach((item) => {
        obj[item.pair] = item
      })
      landingPairs.value = obj
    }
  }
  const setHideAssets = () => {
    isHideAssets.value = !isHideAssets.value
  }
  const getMessageError = async(lang) => {
    const { data } = await getErrors({
      lang: lang ? lang : currentLang
    })
    if (data) {
      errorMessages.value = data
    } else {
      try {
        let times = 0
        const timer = setInterval(async () => {
          if (times > 4) {
            clearInterval(timer)
            return
          }
          times++
          const {
            result: intervalData
          } = await getErrors({
            lang: currentLang
          })

          if (intervalData) {
            errorMessages.value = result
            clearInterval(timer)
          }
        }, 1000)
      } catch (err) {}
    }
  }
  const getCurrencyRate = async() => {
    const { data } = await getCurrencyRateData()
    if (data) {
      currencyRate.value = data
    } else {
      try {
        window.isDispatchCurrencyRate = false
      } catch (err) {}
    }
  }
  const switchExchangeRate = (data) => {
    exchangeRate.value = data
  }
  const getCoinList = async() => {
    const { data } = await listMainCoins()
    if (data) {
      coinList.value = data
    }
  }
  const getAllPairList = async() => {
    const { data } = await getPairList()
    if (data) {
      allPairList.value = data.spot.concat(data.contract)
    }
  }
  const getAssetByCoinList = async() => {
    const { data } = await assetByCoinList()
    if (data) {
      assetAllCoinMap.value = data
    }
  }
  const getAssetsByCoin = async() => {
    const { data } = await assetsByCoin()
    if (data) {
      allAsset.value = {
        'all': data.eq,
        'main': data.mainEq,
        'trade': data.tradeEq,
        'unprofit': data.posmap['USDT'].unprofit,
        'assetmap': data.assetmap
      }
      if (data.arr.length > 0) {
        data.arr.forEach((item) => {
          CoinAssetObj.value[item.asset] = item
        })
      } else {
        CoinAssetObj.value = {}
      }
      const mainArray = data.main
      if (mainArray.length > 0) {
        mainAssetObj.value = {}
        mainArray.forEach((item) => {
          mainAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['main'] = mainArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        mainAssetObj.value = {}
        coinAssetListObj.value['main'] = [
          {
            icon_url: data.assetmap['USDT'].icon_url,
            asset: 'USDT',
            maxTransferOut: 0,
            asset_weight: 0,
            balance: 0,
            balanceUnify: 0,
            c: false,
            collateral: true,
            discount: 1,
            discountForFee: 1,
            discountForMargin: 1,
            eqbtc: 0,
            eqcny: 0,
            equsdt: 0,
            holds: 0,
            total: 0,
            usdtunify: 0,
            withdrawable: 0
          }
        ]
      }
      const tradeArray = data.trade
      if (tradeArray.length > 0) {
        tradeAssetObj.value = {}
        tradeArray.forEach((item) => {
          tradeAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['trade'] = tradeArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        tradeAssetObj.value = {}
        coinAssetListObj.value['trade'] = tradeArray
      }
      tradeArr.value = tradeArray
      posMapObj.value = data.posmap
    }
  }
  const getPairDetail = async(type, pair) => {
    const { data } = await getPairSetting({
      all_spot: 1,
      all_cnt: 1
    })
    if (data) {
      pairInfo.value = data.map((v) => {
        v.price_scale = typeof Number(v.price_scale) === 'number' && !Number.isNaN(Number(v.price_scale)) ? Number(v.price_scale) : 2
        v.quantity_scale = typeof Number(v.quantity_scale) === 'number' && !Number.isNaN(Number(v.quantity_scale)) ? Number(v.quantity_scale) : 4
        return v
      }).reduce((acc, item) => {
        acc[item.symbol] = item
        return acc
      }, {})

      if (process.client) {
        localStorage.setItem('pairInfo', JSON.stringify(pairInfo.value))
      }
    }
  }
  const getDepthSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    const cb = (res) => {
      const convertToNumber = (value) => {
        return typeof value === 'string' ? parseFloat(value) : value;
      }
      if (res.t === 0) { // 全量
        const {
          asks,
          bids
        } = res.d
        const result = {
          pair: res.d.pair,
          asks,
          bids
        }
        depthsStore.value = { [pair]: result }
      } else {
        const {
          add,
          del
        } = res.d
        if (add.asks && depthsStore.value[pair]) {
          add.asks.forEach((v) => {
            depthsStore.value[pair].asks[v.price] = v
          })
        }
        if (add.bids && depthsStore.value[pair]) {
          add.bids.forEach((v) => {
            depthsStore.value[pair].bids[v.price] = v
          })
        }
        if (del.asks && depthsStore.value[pair]) {
          del.asks.forEach((v) => {
            if (Object.values(depthsStore.value[pair].asks).length > 0) {
              delete depthsStore.value[pair].asks[v.price]
            }
          })
        }
        if (del.bids && depthsStore.value[pair]) {
          del.bids.forEach((v) => {
            if (Object.values(depthsStore.value[pair].bids).length > 0) {
              delete depthsStore.value[pair].bids[v.price]
            }
          })
        }
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`, cb)
  }
  const transChartData = (obj) => {
    const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    const finalItem = {
      time: Number(obj[0])
    }
    obj.forEach((v, i) => {
      finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
    })
    return finalItem
  }
  const getKlineList = async(pair: any, time: any) => {
    const { data } = await getKlinesApi({
      symbol: pair,
      market: pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: time,
      limit: 2000
    })
    if (data) {
      klineList.value = data.e.map((item: any) => transChartData(item))
    }
  }
  const getKlineSocket = async(pair: any, time: any) => {
    const cacheKey = `${pair}_${time}`
    
    // 尝试从缓存获取数据
    const cachedData = getCacheEntry('kline', cacheKey)
    if (cachedData) {
      klineList.value = cachedData.klineList || []
      klineTicker.value = cachedData.klineTicker || {}
      logger.debug('K线数据缓存命中', { pair, time, dataLength: klineList.value.length })
      return Promise.resolve()
    }
    
    return new Promise((resolve: Function, reject: Function) => {
      const subscriptionKey = `${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`
      
      try {
        updateDataQuality('socket_connection', true)
        socket.send({"method":"SUBSCRIBE","params":[subscriptionKey]})
        
        const cb = (res) => {
          try {
            // 数据质量检查
            if (!res || !res.d) {
              updateDataQuality('kline_data_error')
              logger.warn('K线数据格式异常', { pair, time, response: res })
              return
            }
            
            // 使用数据处理器验证数据质量
            const processor = getDataProcessor(time)
            let processedData = res.d
            
            if (res.t === 0 && Array.isArray(res.d)) {
              // 全量数据处理
              if (!validateKlineData(res.d)) {
                updateDataQuality('kline_data_error')
                logger.warn('K线全量数据质量验证失败', { pair, time, dataLength: res.d.length })
              } else {
                // 进行数据质量检查和优化
                const qualityResult = processor.validateDataQuality(res.d)
                if (!qualityResult.isValid) {
                  logger.warn('K线数据质量检查发现问题', {
                    pair, time,
                    errors: qualityResult.errors,
                    warnings: qualityResult.warnings
                  })
                  
                  // 尝试修复数据间隙
                  const gaps = processor.detectDataGaps(res.d)
                  if (gaps.length > 0) {
                    updateDataQuality('data_loss')
                    processedData = processor.fillDataGaps(res.d)
                    updateDataQuality('recovery')
                    logger.info('K线数据间隙已修复', { 
                      pair, time, 
                      间隙数量: gaps.length,
                      修复后数据量: processedData.length 
                    })
                  }
                }
              }
              
              klineList.value = processedData
              
              // 缓存处理后的数据
              manageCacheEntry('kline', cacheKey, {
                klineList: processedData,
                klineTicker: klineTicker.value
              }, 180000) // 3分钟缓存
              
              if (processedData.length && time === '1M') {
                klineTicker.value = {
                  ...processedData[processedData.length - 1],
                  currentPair: res.stream.split('.')[1],
                  currentPeriod: time,
                }
                resolve()
                return
              }
            }
            
            // 增量数据处理
            if (res.d.length) {
              const lastData = res.d[res.d.length - 1]
              
              // 实时数据无缝衔接
              if (klineList.value && klineList.value.length > 0) {
                try {
                  const mergedData = processor.seamlessConnect(
                    klineList.value,
                    lastData.close,
                    lastData.volume,
                    lastData.time
                  )
                  
                  if (mergedData.length > 0) {
                    klineList.value = mergedData
                    const finalBar = mergedData[mergedData.length - 1]
                    
                    klineTicker.value = {
                      ...finalBar,
                      currentPair: res.stream.split('.')[1],
                      currentPeriod: time,
                    }
                    
                    logger.debug('K线增量数据无缝衔接成功', {
                      pair, time,
                      price: finalBar.close,
                      time: new Date(finalBar.time).toISOString()
                    })
                  }
                } catch (error) {
                  updateDataQuality('kline_data_error')
                  logger.error('K线数据无缝衔接失败', { pair, time, error: error.message })
                  
                  // 降级处理
                  klineTicker.value = {
                    ...lastData,
                    currentPair: res.stream.split('.')[1],
                    currentPeriod: time,
                  }
                }
              } else {
                klineTicker.value = {
                  ...lastData,
                  currentPair: res.stream.split('.')[1],
                  currentPeriod: time,
                }
              }
              
              // 更新缓存
              manageCacheEntry('kline', cacheKey, {
                klineList: klineList.value,
                klineTicker: klineTicker.value
              }, 180000)
            }
            
            resolve()
            
          } catch (error) {
            updateDataQuality('kline_data_error')
            logger.error('K线数据处理异常', { pair, time, error: error.message })
            resolve() // 仍然resolve，避免阻塞
          }
        }
        
        socket.on(subscriptionKey, cb)
        
      } catch (error) {
        updateDataQuality('socket_connection', false)
        logger.error('K线WebSocket订阅失败', { pair, time, error: error.message })
        
        // 尝试错误恢复
        attemptRecovery('kline_socket', getKlineSocket, pair, time)
          .then(resolve)
          .catch(reject)
      }
    })
  }
  const socketLogin = ref(null)
  const subLogin = () => {
    if (socketLogin.value && socketLogin.value.websocket) {
      socketLogin.value.destroy(); // 使用新增的销毁方法
      socketLogin.value = null;
    }
    socketLogin.value = new SocketClass(`wss://madex-user.tonetou.com`)
    const session_id = cookies.get('session_id_origin') || cookies.get('session_id')
    socketLogin.value.send({"method":"LOGIN","auth":{"sid": session_id}})
    const cb = (res) => { // 资产变化
      getAssetsByCoin()
    }
    const cbO = (res) => { // 订单变化
      if (res && res.data && res.data.product.includes('_SWAP')) {
        isChangeFutureOrder.value = true
      } else {
        isChangeOrder.value = true
      }
    }
    const cb1 = (res) => { // 仓位变化
      isChangePosition.value = true
    }
    socketLogin.value.on('account', cb)
    socketLogin.value.on('order', cbO)
    socketLogin.value.on('position', cb1)
  }
  const reConnectUser = (pair) => {
    if (pair.includes('_SWAP')) {
      isChangePosition.value = true
      isChangeFutureOrder.value = true
    } else {
      isChangeOrder.value = true
    }
    getAssetsByCoin()
  }
  const subTradesSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
    const cb = (res) => {
      if (res.t === 0) {
        dealsObj.value[pair] = res.d
        if (Object.keys(res.d).length > 0) {
          const latestDeal = Object.values(res.d).sort((a, b) => b.t - a.t)[0]
          if (latestDeal) {
            // 确保ticker对象存在，即使初始为空也要创建
            if (!ticker.value[pair]) {
              ticker.value[pair] = {
                last: latestDeal.p,
                product: pair,
                _lastUpdate: Date.now(),
                _source: 'trades'
              }
            } else {
              // 恢复直接更新逻辑，确保数据流不中断
              ticker.value[pair] = {
                ...ticker.value[pair],  
                last: latestDeal.p,
                _lastUpdate: Date.now(),
                _source: 'trades'
              }
            }
          }
        }
      } else {
        Object.values(res.d).forEach((item) => {
          dealsObj.value[pair][item.i] = item
          // 确保ticker对象存在
          if (!ticker.value[pair]) {
            ticker.value[pair] = {
              last: item.p,
              product: pair,
              _lastUpdate: Date.now(),
              _source: 'trades'
            }
          } else {
            const currentPrice = Number(ticker.value[pair].last)
            const newPrice = Number(item.p)
            
            // 只在价格真的发生变化时才更新，避免无意义的更新
            if (currentPrice !== newPrice) {
              ticker.value[pair] = {
                ...ticker.value[pair],
                last: item.p,
                _lastUpdate: Date.now(),
                _source: 'trades'
              }
            }
          }
        })
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`, cb)
  }
  const subTickerSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    const cb = (res) => {
      const product = res.data.product
      const newData = res.data
      const currentData = ticker.value[product]
      
      if (!currentData || 
          currentData.last !== newData.last || 
          currentData.change !== newData.change ||
          currentData.high !== newData.high ||
          currentData.low !== newData.low ||
          currentData.amount !== newData.amount ||
          currentData.volume !== newData.volume) {
        
        // 保留实时成交价格和更新时间戳（如果存在）
        const preservedData = {}
        if (currentData && currentData._lastUpdate) {
          const timeDiff = Date.now() - currentData._lastUpdate
          // 延长时间窗口到10秒，并且只在价格确实不同时才保留
          if (timeDiff < 10000 && Number(currentData.last) !== Number(newData.last)) {
            preservedData.last = currentData.last
            preservedData._lastUpdate = currentData._lastUpdate
            preservedData._source = 'trades' // 标记数据来源
          }
        }
        
        // 恢复直接更新逻辑，确保数据流不中断
        ticker.value[product] = {
          ...newData,
          _source: 'ticker', // 标记默认来源
          ...preservedData
        }
        
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`, cb)
  }
  const subAllTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["ALL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        marketsObj.value[item.product] = item
      })
    }
    socketAllTicker.on('ALL.ticker', cb)
  }
  const subCOLLATERALTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["COLLATERAL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        COLLATERALSymbol.value[item.symbol] = item
      })
    }
    socketAllTicker.on('COLLATERAL.ticker', cb)
  }
  const cancelCOLLATERALTickerSocket = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["COLLATERAL.ticker"]})
  }
  const cancelAllTicker = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["ALL.ticker"]})
  }
  const cancelKline = (pair: any, time: any) => {
    socket.send({"method":"UNSUBSCRIBE", "params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`]})
  }
  const cancelSocket = (pair: any) => {
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
  }
  // 获取系统状态概览
  const getSystemOverview = () => {
    return {
      dataQuality: globalDataQuality.value,
      cacheStats: {
        hitRate: cacheManager.value.totalCacheRequests > 0 ? 
          (cacheManager.value.cacheHitRate / cacheManager.value.totalCacheRequests * 100).toFixed(2) + '%' : '0%',
        totalRequests: cacheManager.value.totalCacheRequests,
        memoryUsage: cacheManager.value.cacheMemoryUsage,
        klineCacheSize: cacheManager.value.klineCache.size,
        tickerCacheSize: cacheManager.value.tickerCache.size,
        depthCacheSize: cacheManager.value.depthCache.size
      },
      errorRecovery: {
        isRecovering: errorRecovery.value.isRecovering,
        activeRetries: errorRecovery.value.retryAttempts.size,
        criticalErrorCount: errorRecovery.value.criticalErrors.length,
        lastRecoveryTime: errorRecovery.value.lastRecoveryTime
      },
      dataProcessors: {
        activeProcessors: dataProcessors.size,
        availableResolutions: Array.from(dataProcessors.keys())
      }
    }
  }
  
  // 重置所有监控数据
  const resetAllMetrics = () => {
    globalDataQuality.value = {
      totalSocketConnections: 0,
      successfulSocketConnections: 0,
      failedSocketConnections: 0,
      websocketErrors: 0,
      klineDataErrors: 0,
      priceDataErrors: 0,
      lastHealthCheck: Date.now(),
      connectionUptime: 0,
      averageLatency: 0,
      dataLossEvents: 0,
      recoveryEvents: 0
    }
    
    cacheManager.value = {
      klineCache: new Map(),
      tickerCache: new Map(),
      depthCache: new Map(),
      lastCleanupTime: Date.now(),
      cacheHitRate: 0,
      cacheMissRate: 0,
      totalCacheRequests: 0,
      cacheMemoryUsage: 0
    }
    
    errorRecovery.value.retryAttempts.clear()
    errorRecovery.value.criticalErrors = []
    dataProcessors.clear()
    
    logger.info('所有监控指标已重置')
  }

  return {
    assetAllCoinMap,
    allAsset,
    coinAssetListObj,
    CoinAssetObj,
    mainAssetObj,
    tradeAssetObj,
    posMapObj,
    errorMessages,
    currencyRate,
    exchangeRate,
    coinList,
    depthsStore,
    ticker,
    klineList,
    klineTicker,
    allPairList,
    dealsObj,
    isChangeFutureOrder,
    isChangeOrder,
    isChangePosition,
    orderChangeObj,
    isHideAssets,
    marketsObj,
    pairInfo,
    priceScale,
    quantityScale,
    isPairDetail,
    landingPairs,
    tradeArr,
    COLLATERALSymbol,
    downLoadInfo,
    pair,
    subCOLLATERALTickerSocket,
    cancelCOLLATERALTickerSocket,
    getLandingPairs,
    getPairDetail,
    setHideAssets,
    switchExchangeRate,
    getCurrencyRate,
    getAssetByCoinList,
    getAllPairList,
    cancelKline,
    getKlineSocket,
    getMessageError,
    getCoinList,
    getAssetsByCoin,
    getDepthSocket,
    subLogin,
    subAllTickerSocket,
    cancelAllTicker,
    subTradesSocket,
    subTickerSocket,
    cancelSocket,
    getDownLoadInfo,
    reConnectUser,
    changePair,
    priceOptimizer,
    
    // 新增：数据质量监控和缓存管理接口
    globalDataQuality,
    cacheManager,
    errorRecovery,
    updateDataQuality,
    manageCacheEntry,
    getCacheEntry,
    cleanupExpiredCache,
    clearNamespaceCache,
    getCacheConflictReport,
    attemptRecovery,
    performHealthCheck,
    getDataProcessor,
    getSystemOverview,
    resetAllMetrics,
    
    // 新增：日志器接口
    logger
  }
})