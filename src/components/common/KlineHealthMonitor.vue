<template>
  <div v-if="isDevelopment" class="kline-health-monitor">
    <div class="monitor-toggle" @click="showMonitor = !showMonitor">
      <span class="monitor-icon" :class="{ active: showMonitor }">📊</span>
      <span class="status-indicator" :class="healthStatus">●</span>
    </div>
    
    <transition name="slide-down">
      <div v-if="showMonitor" class="monitor-panel">
        <div class="monitor-header">
          <h3>K线系统健康监控</h3>
          <div class="actions">
            <button @click="runQuickValidation" :disabled="isRunningValidation" class="btn-validate">
              {{ isRunningValidation ? '验证中...' : '快速验证' }}
            </button>
            <button @click="runFullValidation" :disabled="isRunningValidation" class="btn-validate full">
              {{ isRunningValidation ? '验证中...' : '完整验证' }}
            </button>
            <button @click="runComprehensiveDiagnostic" :disabled="isRunningValidation" class="btn-validate comprehensive">
              {{ isRunningValidation ? '诊断中...' : '综合诊断' }}
            </button>
            <button @click="downloadReport" class="btn-download">下载报告</button>
          </div>
        </div>
        
        <div class="monitor-content">
          <!-- 性能指标 -->
          <div class="metrics-section">
            <h4>性能指标</h4>
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-label">内存使用</div>
                <div class="metric-value" :class="getMetricStatus(performanceStats.memoryUsage, 80)">
                  {{ performanceStats.memoryUsage.toFixed(1) }}%
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">缓存命中率</div>
                <div class="metric-value" :class="getMetricStatus(performanceStats.cacheHitRate, 70, true)">
                  {{ performanceStats.cacheHitRate.toFixed(1) }}%
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">渲染时间</div>
                <div class="metric-value" :class="getMetricStatus(performanceStats.avgRenderTime, 50)">
                  {{ performanceStats.avgRenderTime.toFixed(1) }}ms
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">网络延迟</div>
                <div class="metric-value" :class="getMetricStatus(performanceStats.networkLatency, 1000)">
                  {{ performanceStats.networkLatency.toFixed(0) }}ms
                </div>
              </div>
            </div>
          </div>
          
          <!-- 统一管理器状态 -->
          <div v-if="unifiedManagerOverview" class="manager-section">
            <h4>统一管理器状态</h4>
            <div class="manager-grid">
              <div class="manager-item">
                <span class="manager-label">当前版本:</span>
                <span class="manager-value" :class="getVersionStatus(unifiedManagerOverview.currentVersion)">
                  {{ unifiedManagerOverview.currentVersion === 'basic' ? '基础版' : '专业版' }}
                </span>
              </div>
              <div class="manager-item">
                <span class="manager-label">数据质量:</span>
                <span class="manager-value" :class="getQualityStatus(unifiedManagerOverview.dataQuality.errorRate)">
                  {{ (100 - unifiedManagerOverview.dataQuality.errorRate * 100).toFixed(1) }}%
                </span>
              </div>
              <div class="manager-item">
                <span class="manager-label">同步状态:</span>
                <span class="manager-value" :class="getSyncStatus(unifiedManagerOverview.syncStatus)">
                  {{ formatSyncStatus(unifiedManagerOverview.syncStatus) }}
                </span>
              </div>
              <div class="manager-item">
                <span class="manager-label">切换时间:</span>
                <span class="manager-value" :class="getMetricStatus(unifiedManagerOverview.performance.switchingTime, 2000)">
                  {{ unifiedManagerOverview.performance.switchingTime.toFixed(0) }}ms
                </span>
              </div>
              <div class="manager-item">
                <span class="manager-label">活跃处理器:</span>
                <span class="manager-value">{{ unifiedManagerOverview.activeProcessors }}</span>
              </div>
              <div class="manager-item">
                <span class="manager-label">订阅者数量:</span>
                <span class="manager-value">{{ unifiedManagerOverview.subscriberCount }}</span>
              </div>
            </div>
            
            <!-- 缓存状态详情 -->
            <div class="cache-status">
              <h5>缓存状态:</h5>
              <div class="cache-grid">
                <div class="cache-item" :class="unifiedManagerOverview.cacheStatus.basic === 'active' ? 'active' : 'inactive'">
                  <span>基础版: {{ unifiedManagerOverview.cacheStatus.basic }}</span>
                </div>
                <div class="cache-item" :class="unifiedManagerOverview.cacheStatus.professional === 'active' ? 'active' : 'inactive'">
                  <span>专业版: {{ unifiedManagerOverview.cacheStatus.professional }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数据质量监控 -->
          <div class="quality-section">
            <h4>数据质量</h4>
            <div class="quality-grid">
              <div class="quality-item">
                <span class="quality-label">总更新:</span>
                <span class="quality-value">{{ globalDataQuality.totalUpdates }}</span>
              </div>
              <div class="quality-item">
                <span class="quality-label">有效更新:</span>
                <span class="quality-value success">{{ globalDataQuality.validUpdates }}</span>
              </div>
              <div class="quality-item">
                <span class="quality-label">数据错误:</span>
                <span class="quality-value" :class="globalDataQuality.dataErrors > 0 ? 'error' : 'success'">
                  {{ globalDataQuality.dataErrors }}
                </span>
              </div>
              <div class="quality-item">
                <span class="quality-label">恢复事件:</span>
                <span class="quality-value">{{ globalDataQuality.recoveryEvents }}</span>
              </div>
            </div>
          </div>
          
          <!-- 警告信息 -->
          <div v-if="performanceWarnings.length > 0" class="warnings-section">
            <h4>性能警告</h4>
            <div class="warnings-list">
              <div v-for="(warning, index) in performanceWarnings" :key="index" class="warning-item">
                ⚠️ {{ warning }}
              </div>
            </div>
          </div>
          
          <!-- 验证结果 -->
          <div v-if="validationResult" class="validation-section">
            <h4>验证结果</h4>
            <div class="validation-summary">
              <div class="validation-status" :class="validationResult.overall.success ? 'success' : 'error'">
                {{ validationResult.overall.success ? '✅ 通过' : '❌ 失败' }}
                (评分: {{ validationResult.overall.score }}/100)
              </div>
              <div class="validation-duration">
                耗时: {{ validationResult.overall.duration }}ms
              </div>
            </div>
            
            <div class="components-status">
              <div 
                v-for="(component, name) in validationResult.components" 
                :key="name"
                class="component-status"
                :class="component.status"
              >
                <span class="component-name">{{ name }}</span>
                <span class="component-score">{{ component.score }}/100</span>
              </div>
            </div>
            
            <div v-if="validationResult.recommendations.length > 0" class="recommendations">
              <h5>优化建议:</h5>
              <ul>
                <li v-for="(rec, index) in validationResult.recommendations" :key="index">
                  {{ rec }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useGlobalPerformanceMonitor } from '~/composables/usePerformanceMonitor'
import { createIntegrationValidator, quickValidation, fullValidation } from '~/utils/integrationValidator'
import { createComprehensiveDiagnostics, runQuickDiagnostics, generateDiagnosticReport } from '~/utils/comprehensiveDiagnostics'
import { commonStore } from '~/stores/commonStore'
import { KlineLogger } from '~/config/kline.config'
import { getUnifiedKlineManager } from '~/utils/unifiedKlineManager'

const logger = KlineLogger.getInstance()
const store = commonStore()
const { globalDataQuality } = storeToRefs(store)

// 检查是否为开发环境
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development' || 
         typeof window !== 'undefined' && window.location.hostname === 'localhost'
})

// 组件状态
const showMonitor = ref(false)
const isRunningValidation = ref(false)
const validationResult = ref(null)

// 性能监控数据
const { getGlobalMetrics, getGlobalReport } = useGlobalPerformanceMonitor()

// 默认性能数据
const defaultPerformanceStats = {
  memoryUsage: 0,
  cacheHitRate: 0,
  avgRenderTime: 0,
  networkLatency: 0
}

const performanceStats = ref(defaultPerformanceStats)
const performanceWarnings = ref([])

// 统一管理器状态
const unifiedManagerOverview = ref(null)
const klineManager = getUnifiedKlineManager()

// 健康状态
const healthStatus = computed(() => {
  const memoryOk = performanceStats.value.memoryUsage < 80
  const cacheOk = performanceStats.value.cacheHitRate > 70
  const renderOk = performanceStats.value.avgRenderTime < 50
  const networkOk = performanceStats.value.networkLatency < 1000
  
  const score = [memoryOk, cacheOk, renderOk, networkOk].filter(Boolean).length
  
  if (score >= 3) return 'healthy'
  if (score >= 2) return 'warning'
  return 'critical'
})

// 获取指标状态
const getMetricStatus = (value: number, threshold: number, reverse = false) => {
  if (reverse) {
    return value >= threshold ? 'good' : value >= threshold * 0.7 ? 'warning' : 'bad'
  } else {
    return value <= threshold ? 'good' : value <= threshold * 1.5 ? 'warning' : 'bad'
  }
}

// 获取版本状态
const getVersionStatus = (version: string) => {
  return version === 'basic' ? 'basic-version' : 'professional-version'
}

// 获取数据质量状态
const getQualityStatus = (errorRate: number) => {
  if (errorRate < 0.01) return 'good'
  if (errorRate < 0.05) return 'warning'
  return 'bad'
}

// 获取同步状态
const getSyncStatus = (syncStatus: any) => {
  if (!syncStatus) return 'unknown'
  
  const { basicVersion, professionalVersion, crossVersionConsistency } = syncStatus
  
  if (basicVersion === 'synced' && professionalVersion === 'synced' && crossVersionConsistency) {
    return 'good'
  } else if (basicVersion === 'syncing' || professionalVersion === 'syncing') {
    return 'warning'
  } else if (basicVersion === 'error' || professionalVersion === 'error') {
    return 'bad'
  }
  
  return 'warning'
}

// 格式化同步状态
const formatSyncStatus = (syncStatus: any) => {
  if (!syncStatus) return '未知'
  
  const { basicVersion, professionalVersion, crossVersionConsistency } = syncStatus
  
  if (basicVersion === 'synced' && professionalVersion === 'synced' && crossVersionConsistency) {
    return '已同步'
  } else if (basicVersion === 'syncing' || professionalVersion === 'syncing') {
    return '同步中'
  } else if (basicVersion === 'error' || professionalVersion === 'error') {
    return '同步错误'
  } else if (!crossVersionConsistency) {
    return '数据不一致'
  }
  
  return '部分同步'
}

// 更新性能指标
const updatePerformanceStats = () => {
  try {
    const metrics = getGlobalMetrics()
    if (metrics) {
      performanceStats.value = {
        memoryUsage: metrics.memoryUsage.percentage || 0,
        cacheHitRate: metrics.cachePerformance.hitRate || 0,
        avgRenderTime: metrics.renderingPerformance.averageFrameTime || 0,
        networkLatency: metrics.networkPerformance.avgLatency || 0
      }
      
      // 更新警告信息
      const report = getGlobalReport()
      if (report) {
        performanceWarnings.value = report.recommendations || []
      }
    }
    
    // 更新统一管理器状态
    updateUnifiedManagerStatus()
    
  } catch (error) {
    logger.error('更新性能指标失败', error)
  }
}

// 更新统一管理器状态
const updateUnifiedManagerStatus = () => {
  try {
    if (klineManager) {
      const overview = klineManager.getSystemOverview()
      unifiedManagerOverview.value = overview
      
      logger.debug('统一管理器状态已更新', overview)
    } else {
      unifiedManagerOverview.value = null
      logger.debug('统一管理器未初始化')
    }
  } catch (error) {
    logger.error('更新统一管理器状态失败', error)
    unifiedManagerOverview.value = null
  }
}

// 快速验证
const runQuickValidation = async () => {
  isRunningValidation.value = true
  try {
    logger.info('开始快速验证')
    const result = await quickValidation()
    validationResult.value = result
    logger.info('快速验证完成', result)
  } catch (error) {
    logger.error('快速验证失败', error)
  } finally {
    isRunningValidation.value = false
  }
}

// 完整验证
const runFullValidation = async () => {
  isRunningValidation.value = true
  try {
    logger.info('开始完整验证')
    const result = await fullValidation()
    validationResult.value = result
    logger.info('完整验证完成', result)
  } catch (error) {
    logger.error('完整验证失败', error)
  } finally {
    isRunningValidation.value = false
  }
}

// 综合诊断
const runComprehensiveDiagnostic = async () => {
  isRunningValidation.value = true
  try {
    logger.info('开始综合诊断')
    const result = await runQuickDiagnostics()
    
    // 将综合诊断结果转换为验证结果格式以便显示
    validationResult.value = {
      overall: {
        success: result.status !== 'critical',
        score: result.overallScore,
        duration: Date.now() - result.timestamp
      },
      components: {
        performance: {
          status: result.categories.performance.status,
          score: result.categories.performance.score
        },
        dataQuality: {
          status: result.categories.dataQuality.status,
          score: result.categories.dataQuality.score
        },
        systemHealth: {
          status: result.categories.systemHealth.status,
          score: result.categories.systemHealth.score
        },
        unifiedManager: {
          status: result.categories.unifiedManager.status,
          score: result.categories.unifiedManager.score
        }
      },
      recommendations: result.recommendations,
      diagnosticResult: result // 保存完整的诊断结果用于报告生成
    }
    
    logger.info('综合诊断完成', {
      overallScore: result.overallScore,
      status: result.status,
      criticalIssues: result.criticalIssues.length
    })
  } catch (error) {
    logger.error('综合诊断失败', error)
  } finally {
    isRunningValidation.value = false
  }
}

// 下载报告
const downloadReport = () => {
  try {
    if (!validationResult.value) {
      alert('请先运行验证或诊断')
      return
    }
    
    let htmlReport: string
    let filename: string
    
    // 检查是否有综合诊断结果
    if (validationResult.value.diagnosticResult) {
      // 使用综合诊断报告
      htmlReport = generateDiagnosticReport(validationResult.value.diagnosticResult)
      filename = `kline-comprehensive-diagnostic-${new Date().toISOString().slice(0, 19)}.html`
      logger.info('生成综合诊断报告')
    } else {
      // 使用原有的验证报告
      const validator = createIntegrationValidator()
      htmlReport = validator.generateHTMLReport(validationResult.value)
      filename = `kline-health-report-${new Date().toISOString().slice(0, 19)}.html`
      logger.info('生成健康验证报告')
    }
    
    const blob = new Blob([htmlReport], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    logger.info('报告已下载', { filename })
  } catch (error) {
    logger.error('下载报告失败', error)
  }
}

// 定时更新性能指标
let updateInterval: NodeJS.Timeout | null = null

onMounted(() => {
  if (isDevelopment.value) {
    updatePerformanceStats()
    updateInterval = setInterval(updatePerformanceStats, 5000) // 每5秒更新一次
    logger.info('K线健康监控组件已启动')
  }
})

onBeforeUnmount(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
})
</script>

<style lang="scss" scoped>
.kline-health-monitor {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 9999;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.monitor-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  
  .monitor-icon {
    font-size: 16px;
    transition: transform 0.2s;
    
    &.active {
      transform: rotate(180deg);
    }
  }
  
  .status-indicator {
    font-size: 8px;
    
    &.healthy { color: #4CAF50; }
    &.warning { color: #FF9800; }
    &.critical { color: #F44336; }
  }
}

.monitor-panel {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 400px;
  max-height: 600px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
  
  button {
    padding: 4px 8px;
    font-size: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &.btn-validate {
      background: #2196F3;
      color: white;
      
      &.full {
        background: #9C27B0;
      }
      
      &.comprehensive {
        background: #FF5722;
      }
      
      &:disabled {
        background: #666;
        cursor: not-allowed;
      }
    }
    
    &.btn-download {
      background: #4CAF50;
      color: white;
    }
    
    &:hover:not(:disabled) {
      opacity: 0.8;
    }
  }
}

.monitor-content {
  padding: 12px;
}

.metrics-section {
  margin-bottom: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #FFF;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.metric-card {
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  text-align: center;
  
  .metric-label {
    font-size: 10px;
    color: #CCC;
    margin-bottom: 4px;
  }
  
  .metric-value {
    font-size: 14px;
    font-weight: 600;
    
    &.good { color: #4CAF50; }
    &.warning { color: #FF9800; }
    &.bad { color: #F44336; }
  }
}

.quality-section {
  margin-bottom: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
  }
}

.quality-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.quality-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  
  .quality-label {
    font-size: 10px;
    color: #CCC;
  }
  
  .quality-value {
    font-size: 10px;
    font-weight: 600;
    
    &.success { color: #4CAF50; }
    &.error { color: #F44336; }
  }
}

.warnings-section, .validation-section {
  margin-bottom: 16px;
  
  h4, h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
  }
}

.warnings-list {
  .warning-item {
    padding: 4px 8px;
    background: rgba(255, 152, 0, 0.1);
    border-left: 3px solid #FF9800;
    margin-bottom: 4px;
    font-size: 10px;
  }
}

.validation-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  .validation-status {
    font-weight: 600;
    
    &.success { color: #4CAF50; }
    &.error { color: #F44336; }
  }
  
  .validation-duration {
    font-size: 10px;
    color: #CCC;
  }
}

.components-status {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  margin-bottom: 8px;
}

.component-status {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  border-radius: 2px;
  font-size: 10px;
  
  &.healthy { background: rgba(76, 175, 80, 0.1); }
  &.warning { background: rgba(255, 152, 0, 0.1); }
  &.critical { background: rgba(244, 67, 54, 0.1); }
  
  .component-score {
    font-weight: 600;
  }
}

.recommendations {
  ul {
    margin: 4px 0;
    padding-left: 16px;
    
    li {
      font-size: 10px;
      color: #CCC;
      margin-bottom: 2px;
    }
  }
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 统一管理器状态样式 */
.manager-section {
  margin-bottom: 16px;
  
  h4, h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #FFF;
  }
  
  h5 {
    font-size: 10px;
    margin-top: 8px;
  }
}

.manager-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  margin-bottom: 8px;
}

.manager-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  
  .manager-label {
    font-size: 10px;
    color: #CCC;
  }
  
  .manager-value {
    font-size: 10px;
    font-weight: 600;
    
    &.good { color: #4CAF50; }
    &.warning { color: #FF9800; }
    &.bad { color: #F44336; }
    &.basic-version { color: #2196F3; }
    &.professional-version { color: #9C27B0; }
    &.unknown { color: #666; }
  }
}

.cache-status {
  .cache-grid {
    display: flex;
    gap: 8px;
  }
  
  .cache-item {
    padding: 4px 8px;
    border-radius: 2px;
    font-size: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &.active {
      background: rgba(76, 175, 80, 0.1);
      border-color: #4CAF50;
      color: #4CAF50;
    }
    
    &.inactive {
      background: rgba(244, 67, 54, 0.1);
      border-color: #F44336;
      color: #F44336;
    }
  }
}
</style>