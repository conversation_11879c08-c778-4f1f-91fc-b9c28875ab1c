<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(widget1) // 这段代码不要删
import { commonStore } from '~/stores/commonStore'
import { createKlineProcessor, validateKlineData, type KlineBarData } from '~/utils/klineDataProcessor'
import { KlineLogger } from '~/config/kline.config'
import { usePerformanceMonitor } from '~/composables/usePerformanceMonitor'
const store = commonStore()
const { pairInfo, isPairDetail, pair, ticker, klineList, klineTicker } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]

// 初始化日志记录器
const logger = KlineLogger.getInstance()

// 性能监控
const {
  performanceStats,
  performanceWarnings,
  isPerformanceGood,
  performanceScore,
  trackDataProcessing,
  trackNetworkRequest,
  trackCacheAccess
} = usePerformanceMonitor({
  enableComponentTracking: true,
  enableDataFlowTracking: true,
  enableNetworkTracking: true,
  enableCacheTracking: true,
  autoOptimize: true,
  renderThreshold: 50
})

// 专业版初始化状态管理
const initializationState = ref({
  phase: 'waiting', // waiting -> websocket_check -> widget_init -> data_subscription -> ready
  websocketReady: false,
  widgetReady: false,
  dataSubscribed: false,
  initialDataReceived: false,
  startTime: 0,
  phaseTimings: {},
  retryAttempts: 0,
  maxRetryAttempts: 3,
  errors: []
})

const initializationTimer = ref(null)
const subscriptionDelayTimer = ref(null)

// 专业版初始化管理函数
const updateInitializationPhase = (newPhase: string, error?: any) => {
  const state = initializationState.value
  const now = Date.now()
  
  // 记录阶段时间
  if (state.phase !== 'waiting') {
    state.phaseTimings[state.phase] = now - (state.phaseTimings[state.phase] || state.startTime)
  }
  
  const previousPhase = state.phase
  state.phase = newPhase
  
  if (newPhase !== 'waiting') {
    state.phaseTimings[newPhase] = now
  }
  
  if (error) {
    state.errors.push({
      phase: previousPhase,
      error: error.message || error,
      timestamp: now
    })
  }
  
  logger.info('专业版初始化阶段变更', {
    from: previousPhase,
    to: newPhase,
    elapsed: now - state.startTime,
    error: error?.message
  })
}

// WebSocket状态检查
const checkWebSocketReadiness = (): Promise<boolean> => {
  return new Promise((resolve) => {
    updateInitializationPhase('websocket_check')
    
    // 检查store中的连接状态
    const store = commonStore()
    
    // 模拟WebSocket状态检查
    const checkConnection = () => {
      // 检查是否有活跃的ticker数据
      const hasActiveTicker = Object.keys(ticker.value).length > 0
      
      // 检查最近是否有数据更新
      const currentPair = props.pair || pair.value
      const recentUpdate = ticker.value[currentPair]?._lastUpdate
      const isRecentlyUpdated = recentUpdate && (Date.now() - recentUpdate) < 30000
      
      if (hasActiveTicker || isRecentlyUpdated) {
        logger.info('专业版WebSocket连接状态良好')
        initializationState.value.websocketReady = true
        resolve(true)
      } else {
        logger.debug('专业版等待WebSocket连接建立')
        // 继续检查，最多等待10秒
        setTimeout(checkConnection, 1000)
      }
    }
    
    // 开始检查，设置超时
    checkConnection()
    
    setTimeout(() => {
      if (!initializationState.value.websocketReady) {
        logger.warn('专业版WebSocket连接检查超时，继续初始化')
        initializationState.value.websocketReady = true
        resolve(false)
      }
    }, 10000)
  })
}

// 延迟数据订阅
const delayedDataSubscription = async (delay: number = 2000) => {
  updateInitializationPhase('data_subscription')
  
  return new Promise((resolve) => {
    subscriptionDelayTimer.value = setTimeout(async () => {
      try {
        logger.info('专业版开始延迟数据订阅', { delay })
        
        const currentPair = props.pair || pair.value
        
        // 确保有pair数据后再订阅
        if (currentPair) {
          // 主动触发K线数据订阅
          await store.getKlineSocket(currentPair, props.resolution)
          
          initializationState.value.dataSubscribed = true
          logger.info('专业版数据订阅成功')
          
          // 监听初始数据接收
          waitForInitialData()
        }
        
        resolve(true)
      } catch (error) {
        logger.error('专业版数据订阅失败', error)
        updateInitializationPhase('data_subscription', error)
        resolve(false)
      }
    }, delay)
  })
}

// 等待初始数据
const waitForInitialData = () => {
  const checkDataReceived = () => {
    const hasKlineData = klineList.value && klineList.value.length > 0
    const hasTicker = ticker.value[props.pair || pair.value]
    
    if (hasKlineData || hasTicker) {
      logger.info('专业版接收到初始数据')
      initializationState.value.initialDataReceived = true
      updateInitializationPhase('ready')
      return
    }
    
    // 继续等待，最多15秒
    if (Date.now() - initializationState.value.startTime < 15000) {
      setTimeout(checkDataReceived, 1000)
    } else {
      logger.warn('专业版初始数据接收超时')
      updateInitializationPhase('ready', new Error('初始数据接收超时'))
    }
  }
  
  checkDataReceived()
}

// 智能初始化流程
const smartInitialization = async () => {
  const state = initializationState.value
  
  // 重置状态
  state.startTime = Date.now()
  state.phase = 'waiting'
  state.websocketReady = false
  state.widgetReady = false
  state.dataSubscribed = false
  state.initialDataReceived = false
  state.phaseTimings = {}
  state.errors = []
  
  logger.info('专业版开始智能初始化流程')
  
  try {
    // 阶段1: 检查WebSocket准备状态
    await checkWebSocketReadiness()
    
    // 阶段2: 初始化TradingView Widget
    updateInitializationPhase('widget_init')
    
    // 检查是否需要初始化图表
    const currentPair = props.pair || pair.value
    if (currentPair && JSON.stringify(pairInfo.value) !== '{}') {
      await initChart()
      state.widgetReady = true
    }
    
    // 阶段3: 延迟数据订阅（确保widget准备好）
    await delayedDataSubscription(1500)
    
    logger.info('专业版智能初始化完成', {
      totalTime: Date.now() - state.startTime,
      phaseTimings: state.phaseTimings,
      errors: state.errors.length
    })
    
  } catch (error) {
    logger.error('专业版智能初始化失败', error)
    
    // 重试机制
    if (state.retryAttempts < state.maxRetryAttempts) {
      state.retryAttempts++
      logger.info('专业版初始化重试', { attempt: state.retryAttempts })
      
      setTimeout(() => smartInitialization(), 2000 * state.retryAttempts)
    } else {
      logger.error('专业版初始化达到最大重试次数')
      updateInitializationPhase('ready', error)
    }
  }
}

// 创建K线数据处理器实例
const dataProcessor = computed(() => createKlineProcessor(props.resolution))

// 数据质量监控状态
const lastValidDataTime = ref(0)
const lastUpdateTime = ref(0)
const dataQualityMetrics = ref({
  totalUpdates: 0,
  validUpdates: 0,
  invalidUpdates: 0,
  seamlessConnections: 0,
  lastUpdateTime: 0
})

// 1日周期智能重建管理
const dailyRebuildManager = ref({
  lastRebuildTime: 0,
  rebuildCount: 0,
  dataContamination: {
    score: 0,
    sources: new Set(),
    lastCheck: 0
  },
  nativeResolutionSupported: true,
  forceRebuildReasons: new Set()
})

// 智能重建策略函数
const assessDataContamination = (oldResolution: string, newResolution: string): number => {
  const manager = dailyRebuildManager.value
  let contaminationScore = 0
  
  // 重置污染检查
  manager.dataContamination.sources.clear()
  manager.dataContamination.lastCheck = Date.now()
  
  // 评估因素1: 分辨率跨度大小
  const resolutionMap = {
    '1m': 1, '5m': 5, '15m': 15, '30m': 30, '1h': 60, '2h': 120,
    '4h': 240, '6h': 360, '8h': 480, '12h': 720, '1d': 1440, '1w': 10080, '1M': 43200
  }
  
  const oldValue = resolutionMap[oldResolution] || 60
  const newValue = resolutionMap[newResolution] || 1440
  const span = Math.abs(newValue - oldValue)
  
  if (span > 1000) { // 跨度很大
    contaminationScore += 30
    manager.dataContamination.sources.add('large_resolution_span')
  }
  
  // 评估因素2: 月线污染检查
  if (oldResolution === '1M') {
    contaminationScore += 40
    manager.dataContamination.sources.add('monthly_contamination')
    logger.debug('检测到月线数据可能污染1日周期')
  }
  
  // 评估因素3: 基础版数据冲突检查
  const store = commonStore()
  if (store.klineTicker.currentPeriod === '1M' && store.klineList.length > 0) {
    contaminationScore += 25
    manager.dataContamination.sources.add('basic_version_conflict')
    logger.debug('检测到基础版1M数据可能污染')
  }
  
  // 评估因素4: 近期重建频率
  const now = Date.now()
  if (now - manager.lastRebuildTime < 60000) { // 1分钟内
    contaminationScore -= 20 // 减少不必要重建
    manager.dataContamination.sources.add('recent_rebuild_penalty')
  }
  
  // 评估因素5: 数据质量状况
  const errorRate = dataQualityMetrics.value.totalUpdates > 0 ? 
    (dataQualityMetrics.value.invalidUpdates / dataQualityMetrics.value.totalUpdates) : 0
  
  if (errorRate > 0.1) { // 错误率超过10%
    contaminationScore += 15
    manager.dataContamination.sources.add('high_error_rate')
  }
  
  manager.dataContamination.score = contaminationScore
  
  logger.debug('1日周期数据污染评估', {
    oldResolution,
    newResolution,
    score: contaminationScore,
    sources: Array.from(manager.dataContamination.sources),
    recommendation: contaminationScore > 50 ? 'rebuild' : 'native_switch'
  })
  
  return contaminationScore
}

// 判断是否需要重建
const shouldRebuildForDaily = (oldResolution: string): boolean => {
  const contaminationScore = assessDataContamination(oldResolution, '1d')
  const manager = dailyRebuildManager.value
  
  // 强制重建条件 (分数 > 50)
  if (contaminationScore > 50) {
    logger.info('1日周期需要重建', {
      reason: 'high_contamination_score',
      score: contaminationScore,
      sources: Array.from(manager.dataContamination.sources)
    })
    return true
  }
  
  // 检查是否有强制重建原因
  if (manager.forceRebuildReasons.size > 0) {
    logger.info('1日周期强制重建', {
      reasons: Array.from(manager.forceRebuildReasons)
    })
    return true
  }
  
  // 检查TradingView原生支持
  if (!manager.nativeResolutionSupported) {
    logger.info('1日周期需要重建', {
      reason: 'native_resolution_not_supported'
    })
    return true
  }
  
  logger.info('1日周期可使用原生切换', {
    contaminationScore,
    supportNative: manager.nativeResolutionSupported
  })
  
  return false
}

// 获取重建原因描述
const getRebuildReason = (oldResolution: string): string => {
  const manager = dailyRebuildManager.value
  const sources = Array.from(manager.dataContamination.sources)
  
  if (sources.includes('monthly_contamination')) {
    return '月线数据污染'
  }
  if (sources.includes('basic_version_conflict')) {
    return '基础版数据冲突'
  }
  if (sources.includes('large_resolution_span')) {
    return '分辨率跨度过大'
  }
  if (sources.includes('high_error_rate')) {
    return '数据错误率过高'
  }
  if (manager.forceRebuildReasons.size > 0) {
    return Array.from(manager.forceRebuildReasons).join(', ')
  }
  
  return '数据质量保护'
}

// 更新重建统计
const updateRebuildStats = (wasRebuilt: boolean, reason?: string) => {
  const manager = dailyRebuildManager.value
  
  if (wasRebuilt) {
    manager.lastRebuildTime = Date.now()
    manager.rebuildCount++
    logger.info('1日周期重建统计更新', {
      totalRebuildCount: manager.rebuildCount,
      reason
    })
  }
  
  // 清除强制重建原因
  manager.forceRebuildReasons.clear()
}

const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  }
})
const resolutionReMap: any = {
  'line': 1,
  '1m': 1,
  '5m': 5,
  '15m': 15,
  '30m': 30,
  '1h': 60,
  '2h': 120,
  '4h': 240,
  '6h': 360,
  '8h': 480,
  '12h': 720,
  '1d': '1d',
  '1w': '1w',
  '1M': '1M'
}
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting','updateTradingViewData'])
// 类型安全的widgetOption配置
const getSafeSymbol = () => {
  const symbol = props.pair || pair.value
  if (typeof symbol !== 'string' || !symbol) {
    console.warn('[TradingView] Invalid symbol, using default:', symbol)
    return 'ETH_USDT'
  }
  return symbol
}

const getSafeInterval = () => {
  const resolution = props.resolution
  if (!resolution || !resolutionReMap[resolution]) {
    console.warn('[TradingView] Invalid resolution, using default:', resolution)
    return '15'
  }
  return resolutionReMap[resolution]
}

const getSafeLocale = () => {
  const mappedLocale = tradingviewLangMap[locale.value]
  if (mappedLocale && typeof mappedLocale === 'string') {
    return mappedLocale
  }
  if (typeof locale.value === 'string') {
    return locale.value
  }
  return 'en'
}

const widgetOption = {
  debug: false,
  symbol: getSafeSymbol(),
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: getSafeInterval(),
  locale: getSafeLocale(),
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null
const Loading = ref(true)
const isRebuilding = ref(false) // 防止重复重建的状态标记
const resolutionChangeTimeout = ref<NodeJS.Timeout | null>(null) // 防抖延迟
const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }

  // 专业版1日周期修复：初始化时清理可能污染的缓存
  const currentResolution = props.resolution
  if (currentResolution === '1d') {
    try {
      const store = commonStore()
      // 如果store中有1M的缓存数据，清理它以避免污染1日周期
      if (store.klineTicker.currentPeriod === '1M') {
        store.klineList = []
        store.klineTicker = {}
      }
    } catch (error) {
    }
  }

  if (props.resolution === '1M') {
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  datafeedInstance = useDatafeedAction(pairInfo.value)
  
  // 专业版1日周期修复：如果是1日周期，强制清理datafeed缓存
  if (currentResolution === '1d' && datafeedInstance) {
    try {
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(true)
      }
      if (typeof datafeedInstance.setForceRefresh === 'function') {
        datafeedInstance.setForceRefresh(true)
      }
    } catch (error) {
    }
  }
  
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15',
  widget = new window.TradingView.widget(widgetOption)
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
  })
}

const previousResolution = ref(props.resolution)

// 防抖周期切换函数
const debouncedResolutionChange = (newVal: string, oldVal: string) => {
  // 清除之前的延迟
  if (resolutionChangeTimeout.value) {
    clearTimeout(resolutionChangeTimeout.value)
  }
  
  resolutionChangeTimeout.value = setTimeout(() => {
    handleResolutionChange(newVal, oldVal)
  }, 300) // 300ms 防抖延迟
}

const handleResolutionChange = (newVal: string, oldVal: string) => {
  // 防止在重建过程中重复触发
  if (isRebuilding.value) {
    console.warn('[TradingView] Widget is rebuilding, ignoring resolution change')
    return
  }
  
  if (!newVal || !widget || newVal === oldVal) {
    return
  }
  
  const isFromMonthly = oldVal === '1M'
  const isToMonthly = newVal === '1M'
  const isSignificantChange = isFromMonthly !== isToMonthly
  const isToDaily = newVal === '1d'
  
  // 专业版1日周期优化：智能清理缓存数据
  if (isToDaily && datafeedInstance) {
    try {
      // 清理datafeed缓存，避免基本版数据污染
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(true)
      }
      // 清理store中可能冲突的1M相关状态
      const store = commonStore()
      if (store.klineTicker.currentPeriod === '1M') {
        store.klineList = []
        store.klineTicker = {}
      }
    } catch (error) {
      console.warn('[TradingView] Cache cleanup failed:', error)
    }
  }
  
  // 在特殊情况下通知datafeed将有周期切换
  if (datafeedInstance && typeof datafeedInstance.setForceRefresh === 'function' && (isSignificantChange || isToDaily)) {
    try {
      datafeedInstance.setForceRefresh(true)
    } catch (error) {
      console.warn('[TradingView] Failed to set force refresh:', error)
    }
  }
  
  // 专业版1日周期智能重建策略
  if (isToDaily) {
    const needsRebuild = shouldRebuildForDaily(oldVal)
    const rebuildReason = getRebuildReason(oldVal)
    
    logger.info('1日周期切换决策', { 
      oldResolution: oldVal,
      contaminationScore: dailyRebuildManager.value.dataContamination.score,
      needsRebuild,
      reason: rebuildReason,
      sources: Array.from(dailyRebuildManager.value.dataContamination.sources)
    })
    
    if (needsRebuild) {
      logger.info('执行1日周期智能重建', { reason: rebuildReason })
      rebuildWidget(newVal)
      updateRebuildStats(true, rebuildReason)
      return
    } else {
      logger.info('1日周期使用原生切换，避免不必要重建')
      // 尝试原生切换
      try {
        widget.activeChart().setResolution(resolutionReMap[newVal])
        updateRebuildStats(false)
        
        // 测试原生切换是否成功，如果失败则标记为不支持
        setTimeout(() => {
          try {
            const currentResolution = widget.activeChart().resolution()
            if (currentResolution !== resolutionReMap[newVal]) {
              logger.warn('1日周期原生切换可能未成功，下次将使用重建')
              dailyRebuildManager.value.nativeResolutionSupported = false
            }
          } catch (error) {
            logger.warn('无法验证1日周期原生切换结果')
          }
        }, 1000)
        
        logger.info('1日周期原生切换成功')
        return
      } catch (error) {
        logger.warn('1日周期原生切换失败，降级到重建', error)
        dailyRebuildManager.value.nativeResolutionSupported = false
        rebuildWidget(newVal)
        updateRebuildStats(true, '原生切换失败')
        return
      }
    }
  }
  
  // 其他分辨率：优先使用TradingView原生切换
  try {
    widget.activeChart().setResolution(resolutionReMap[newVal])
    logger.debug('分辨率原生切换成功', { from: oldVal, to: newVal })
  } catch (error) {
    console.warn('[TradingView] setResolution failed, rebuilding widget:', error)
    rebuildWidget(newVal)
  }
}

// 精确缓存清理策略 - 增强版
const performPreciseCacheCleanup = (resolution: string, contamination: any) => {
  const store = commonStore()
  const sources = Array.from(contamination.sources)
  const currentPair = props.pair || pair.value
  
  logger.info('1日周期执行精确缓存清理', {
    resolution,
    contaminationSources: sources,
    contaminationScore: contamination.score,
    pair: currentPair
  })
  
  // 创建清理计划，分阶段执行，减少对用户体验的影响
  const cleanupPlan = {
    immediate: [], // 立即清理的项目
    deferred: [],  // 延迟清理的项目
    preserve: []   // 需要保留的数据
  }
  
  // 分析清理需求
  if (sources.includes('monthly_contamination') || sources.includes('basic_version_conflict')) {
    // 月线污染和基础版冲突需要立即清理
    cleanupPlan.immediate.push('monthly_data', 'basic_namespace')
    
    // 保留当前1日周期的有效数据
    if (store.klineTicker.currentPeriod === '1d' && klineList.value.length > 0) {
      cleanupPlan.preserve.push('current_1d_data')
    }
  }
  
  if (sources.includes('large_resolution_span')) {
    // 大跨度切换可以延迟清理，不影响用户立即看到图表
    cleanupPlan.deferred.push('datafeed_cache')
  }
  
  if (sources.includes('high_error_rate')) {
    // 高错误率需要立即清理相关缓存
    cleanupPlan.immediate.push('error_prone_cache')
  }
  
  // 智能数据保留策略
  const preserveValidData = () => {
    if (!klineList.value || klineList.value.length === 0) {
      return []
    }
    
    // 保留策略：优先保留最近的高质量数据
    const now = Date.now()
    const validData = klineList.value.filter(item => {
      // 保留最近24小时的数据
      const itemTime = item.time || item.timestamp
      return itemTime && (now - itemTime) < 86400000 // 24小时
    })
    
    // 如果最近24小时数据不足，保留最后50条
    const finalData = validData.length >= 20 ? validData : klineList.value.slice(-50)
    
    logger.debug('智能数据保留', { 
      originalLength: klineList.value.length,
      validDataLength: validData.length,
      finalPreservedLength: finalData.length,
      preservationRatio: ((finalData.length / klineList.value.length) * 100).toFixed(1) + '%'
    })
    
    return finalData
  }
  
  // 执行立即清理
  const executeImmediateCleanup = () => {
    cleanupPlan.immediate.forEach(task => {
      switch (task) {
        case 'monthly_data':
          if (store.klineTicker.currentPeriod === '1M') {
            logger.debug('立即清理月线污染数据')
            store.klineList = []
            store.klineTicker = {}
          }
          break
          
        case 'basic_namespace':
          try {
            store.clearNamespaceCache('basic')
            logger.debug('立即清理基础版命名空间缓存')
          } catch (error) {
            logger.warn('清理基础版缓存失败', error)
          }
          break
          
        case 'error_prone_cache':
          // 清理最近有错误的缓存条目
          try {
            store.cleanupExpiredCache('professional')
            logger.debug('清理专业版错误缓存')
          } catch (error) {
            logger.warn('清理错误缓存失败', error)
          }
          break
      }
    })
  }
  
  // 执行延迟清理（不阻塞UI）
  const executeDeferredCleanup = () => {
    // 使用requestIdleCallback或setTimeout来延迟执行
    const deferredExecution = () => {
      cleanupPlan.deferred.forEach(task => {
        switch (task) {
          case 'datafeed_cache':
            if (datafeedInstance) {
              try {
                if (typeof datafeedInstance.clearCache === 'function') {
                  // 只清理特定分辨率的缓存，而不是全量清理
                  datafeedInstance.clearCache(false) // false = 选择性清理
                }
                logger.debug('延迟执行datafeed缓存清理')
              } catch (error) {
                logger.warn('延迟datafeed缓存清理失败', error)
              }
            }
            break
        }
      })
      
      logger.debug('延迟清理任务完成', { tasks: cleanupPlan.deferred })
    }
    
    // 优先使用requestIdleCallback，降级到setTimeout
    if (typeof window !== 'undefined') {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(deferredExecution, { timeout: 5000 })
      } else {
        setTimeout(deferredExecution, 100)
      }
    } else {
      setTimeout(deferredExecution, 100)
    }
  }
  
  // 执行清理计划
  executeImmediateCleanup()
  executeDeferredCleanup()
  
  // 返回保留的数据
  const preservedData = preserveValidData()
  
  logger.info('精确缓存清理完成', {
    immediateCleanupTasks: cleanupPlan.immediate.length,
    deferredCleanupTasks: cleanupPlan.deferred.length,
    preservedDataLength: preservedData.length,
    cleanupScore: contamination.score
  })
  
  return preservedData
}

const rebuildWidget = (newVal: string) => {
  if (isRebuilding.value) {
    console.warn('[TradingView] Already rebuilding, skipping')
    return
  }
  
  isRebuilding.value = true
  // 增强的清理超时机制：确保重建状态不会被永久锁定
  const rebuildTimeout = setTimeout(() => {
    if (isRebuilding.value) {
      console.warn('[TradingView] Rebuild timeout, forcing reset')
      isRebuilding.value = false
      Loading.value = false
    }
  }, 15000) // 15秒超时
  
  let preservedData = []
  
  try {
    // 专业版1日周期优化：精确缓存清理
    if (newVal === '1d') {
      logger.info('1日周期重建 - 执行精确清理策略')
      preservedData = performPreciseCacheCleanup(newVal, dailyRebuildManager.value.dataContamination)
    } else {
      // 其他分辨率的标准清理
      if (datafeedInstance) {
        try {
          if (typeof datafeedInstance.cancelAllActiveRequests === 'function') {
            datafeedInstance.cancelAllActiveRequests()
          }
          if (typeof datafeedInstance.clearCache === 'function') {
            datafeedInstance.clearCache(true)
          }
        } catch (error) {
          console.warn('[TradingView] Failed to clear cache:', error)
        }
      }
    }
    
    // 移除旧widget
    if (widget) {
      try {
        widget.remove()
        widget = null
      } catch (error) {
        console.warn('[TradingView] Error removing old widget:', error)
        widget = null // 强制清空
      }
    }
    
    // 重建 widget
    nextTick(() => {
      try {
        clearTimeout(rebuildTimeout) // 清除超时定时器
        Loading.value = true
        datafeedInstance = useDatafeedAction(pairInfo.value)
        
        // 专业版1日周期优化：重建时的智能处理
        if (newVal === '1d' && datafeedInstance) {
          try {
            if (typeof datafeedInstance.setForceRefresh === 'function') {
              datafeedInstance.setForceRefresh(true)
            }
            
            // 如果有保留的有效数据，预加载到datafeed
            if (preservedData.length > 0 && typeof datafeedInstance.preloadData === 'function') {
              datafeedInstance.preloadData(preservedData)
              logger.info('1日周期预加载保留数据', { dataLength: preservedData.length })
            }
          } catch (error) {
            console.warn('[TradingView] Failed to set force refresh during rebuild:', error)
          }
        }
        
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = props.pair || pair.value
        widgetOption.interval = resolutionReMap[newVal]
        widget = new window.TradingView.widget(widgetOption)
        
        widget.onChartReady(() => {
          try {
            widget.activeChart().setChartType(1)
            widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
            
            // 1日周期的渐进式数据恢复 - 增强版
            if (newVal === '1d' && preservedData.length > 0) {
              const performProgressiveRecovery = async () => {
                try {
                  logger.info('1日周期渐进式数据恢复开始', { 
                    dataLength: preservedData.length,
                    recoveryMode: 'progressive'
                  })
                  
                  // 分批恢复策略：避免一次性处理大量数据造成UI阻塞
                  const batchSize = Math.min(10, preservedData.length)
                  const batches = []
                  
                  for (let i = 0; i < preservedData.length; i += batchSize) {
                    batches.push(preservedData.slice(i, i + batchSize))
                  }
                  
                  // 先恢复最关键的数据（最新的几条）
                  const criticalData = preservedData.slice(-5) // 最新5条数据
                  if (criticalData.length > 0) {
                    try {
                      const latestBar = criticalData[criticalData.length - 1]
                      // 更新最新价格，确保用户能立即看到当前价位
                      if (widget.activeChart && typeof widget.activeChart === 'function') {
                        const chart = widget.activeChart()
                        if (chart && typeof chart.updateData === 'function') {
                          chart.updateData(latestBar)
                        }
                      }
                      
                      logger.debug('关键数据恢复完成', { 
                        latestPrice: latestBar.close,
                        latestTime: new Date(latestBar.time).toISOString()
                      })
                    } catch (error) {
                      logger.warn('关键数据恢复失败', error)
                    }
                  }
                  
                  // 逐批恢复历史数据
                  let recoveredCount = 0
                  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                    const batch = batches[batchIndex]
                    
                    // 使用requestAnimationFrame来确保不阻塞UI
                    await new Promise(resolve => {
                      requestAnimationFrame(() => {
                        try {
                          // 这里可以根据TradingView的API来恢复数据
                          // 由于我们已经在criticalData中处理了最新数据
                          // 这里主要是为了恢复历史数据的完整性
                          
                          recoveredCount += batch.length
                          
                          logger.debug(`数据恢复批次${batchIndex + 1}完成`, {
                            batchSize: batch.length,
                            totalRecovered: recoveredCount,
                            totalBatches: batches.length,
                            progress: `${((batchIndex + 1) / batches.length * 100).toFixed(1)}%`
                          })
                          
                          resolve(true)
                        } catch (error) {
                          logger.warn(`数据恢复批次${batchIndex + 1}失败`, error)
                          resolve(false)
                        }
                      })
                    })
                    
                    // 批次间的小延迟，进一步减少性能影响
                    if (batchIndex < batches.length - 1) {
                      await new Promise(resolve => setTimeout(resolve, 16)) // 约1帧的时间
                    }
                  }
                  
                  logger.info('1日周期渐进式数据恢复完成', {
                    totalDataRecovered: recoveredCount,
                    totalBatches: batches.length,
                    success: recoveredCount === preservedData.length
                  })
                  
                  // 恢复完成后，发送恢复完成事件
                  emit('updateTradingViewData', {
                    type: 'recovery_completed',
                    data: {
                      recoveredCount,
                      totalExpected: preservedData.length,
                      success: recoveredCount === preservedData.length
                    },
                    pair: props.pair || pair.value,
                    resolution: newVal,
                    timestamp: Date.now()
                  })
                  
                } catch (error) {
                  logger.error('1日周期渐进式数据恢复失败', error)
                  
                  // 恢复失败时的降级处理
                  emit('updateTradingViewData', {
                    type: 'recovery_failed',
                    data: {
                      error: error.message,
                      preservedDataLength: preservedData.length
                    },
                    pair: props.pair || pair.value,
                    resolution: newVal,
                    timestamp: Date.now()
                  })
                }
              }
              
              // 延迟启动恢复过程，确保widget已经准备好
              setTimeout(performProgressiveRecovery, 300)
            }
            
            Loading.value = false
            isRebuilding.value = false
            
            logger.info('widget重建完成', { 
              resolution: newVal,
              preservedDataLength: preservedData.length
            })
          } catch (error) {
            isRebuilding.value = false
            Loading.value = false
            logger.error('widget初始化失败', error)
          }
        })
      } catch (error) {
        isRebuilding.value = false
        Loading.value = false
        clearTimeout(rebuildTimeout)
        logger.error('widget重建失败', error)
      }
    })
  } catch (error) {
    isRebuilding.value = false
    Loading.value = false
    clearTimeout(rebuildTimeout)
    logger.error('重建过程异常', error)
  }
}

watch(() => props.resolution, (newVal, oldVal) => {
  if (newVal && widget && newVal !== oldVal) {
    debouncedResolutionChange(newVal, oldVal)
  }
  previousResolution.value = newVal
})
watch(() => pair.value, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading) {
    widget.activeChart().setSymbol(newVal)
  }
})

// 监听 props.pair 的变化
watch(() => props.pair, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading) {
    widget.activeChart().setSymbol(newVal)
  }
})
const hasChanged = ref(false)

onMounted(() => {
  logger.info('专业版K线组件初始化开始', {
    pair: props.pair,
    resolution: props.resolution,
    existingDataLength: klineList.value?.length || 0
  })
  
  // 初始化数据质量监控
  dataQualityMetrics.value = {
    totalUpdates: 0,
    validUpdates: 0,
    invalidUpdates: 0,
    seamlessConnections: 0,
    lastUpdateTime: Date.now()
  }
  
  // 启动智能初始化流程
  smartInitialization()
  
  logger.info('专业版K线组件初始化完成')
})

// 分别监听 props.pair 和 pairInfo 的变化
watch(() => props.pair, (newPair, oldPair) => {
  const currentPair = newPair || pair.value
  
  if (!hasChanged.value && currentPair && JSON.stringify(pairInfo.value || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value
  
  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })

// 专业版无缝衔接处理：监听实时ticker数据变化
watch(() => ticker.value[props.pair || pair.value]?._lastUpdate, (newUpdate, oldUpdate) => {
  if (newUpdate && widget && !props.isLoading && !isRebuilding.value && newUpdate !== oldUpdate) {
    return trackDataProcessing(() => {
      const currentPair = props.pair || pair.value
      const currentTicker = ticker.value[currentPair]
      
      if (currentTicker?.last) {
        logger.debug('专业版实时数据更新', {
          pair: currentPair,
          price: currentTicker.last,
          time: newUpdate
        })
        
        try {
          const newTime = Date.now()
          if (newTime > lastUpdateTime.value) {
            lastUpdateTime.value = newTime
            lastValidDataTime.value = newTime
            
            // 数据质量监控
            dataQualityMetrics.value.totalUpdates++
            dataQualityMetrics.value.validUpdates++
            dataQualityMetrics.value.lastUpdateTime = newTime
            
            // 构建实时数据
            const realtimeData: Partial<KlineBarData> = {
              close: Math.abs(Number(currentTicker.last)),
              time: newTime
            }
            
            // 如果有KlineTicker的额外数据，也加入
            if (klineTicker.value?.high) {
              realtimeData.high = Math.abs(Number(klineTicker.value.high))
            }
            if (klineTicker.value?.low) {
              realtimeData.low = Math.abs(Number(klineTicker.value.low))
            }
            if (klineTicker.value?.open) {
              realtimeData.open = Math.abs(Number(klineTicker.value.open))
            }
            if (klineTicker.value?.volume) {
              realtimeData.volume = Math.abs(Number(klineTicker.value.volume))
            }
            
            // 发送更新事件到父组件
            emit('updateTradingViewData', {
              type: 'realtime_update',
              data: realtimeData,
              pair: currentPair,
              resolution: props.resolution,
              timestamp: newTime
            })
            
            // TradingView图表刷新
            if (widget.activeChart && typeof widget.activeChart === 'function') {
              const chart = widget.activeChart()
              
              if (chart && typeof chart.refreshChart === 'function') {
                chart.refreshChart()
              }
            }
            
            logger.debug('专业版实时数据处理成功', {
              time: new Date(newTime).toISOString(),
              price: currentTicker.last
            })
          }
        } catch (error) {
          logger.error('专业版实时数据处理失败', error)
          dataQualityMetrics.value.invalidUpdates++
        }
      }
    }, 'tradingview-realtime-update')
  }
}, { immediate: false })

// 监听klineList数据变化（历史数据加载）- 优化版本
watch(klineList, (val) => {
  return trackDataProcessing(() => {
    logger.debug('专业版klineList数据变化', { dataLength: val?.length || 0 })
    
    if (!val || val.length === 0) {
      logger.debug('专业版klineList数据为空，跳过处理')
      trackCacheAccess(false)
      return
    }
    
    trackCacheAccess(true)
    
    // 优化：只对大量数据进行质量检查，避免阻塞小量数据更新
    const shouldValidate = val.length > 100 || dataQualityMetrics.value.totalUpdates % 10 === 0
    
    let processedData = val
    
    if (shouldValidate) {
      // 异步进行数据质量检查，不阻塞主数据流
      nextTick(() => {
        try {
          const normalizedData = dataProcessor.value.normalizeData(val)
          const qualityResult = dataProcessor.value.validateDataQuality(normalizedData)
          
          if (!qualityResult.isValid && qualityResult.errors.length > 5) {
            logger.warn('专业版K线数据质量问题较多，尝试修复', {
              errorCount: qualityResult.errors.length,
              warningCount: qualityResult.warnings.length
            })
            
            // 只在错误较多时才修复
            const repairedData = dataProcessor.value.fillDataGaps(normalizedData)
            if (repairedData.length > normalizedData.length) {
              logger.info('专业版数据修复完成', { 
                原始数据量: normalizedData.length,
                修复后数据量: repairedData.length
              })
              
              emit('updateTradingViewData', {
                type: 'data_repair',
                data: repairedData,
                pair: props.pair,
                resolution: props.resolution,
                timestamp: Date.now()
              })
            }
          }
        } catch (error) {
          logger.warn('专业版异步数据质量检查失败', error)
        }
      })
      
      // 使用标准化数据
      processedData = dataProcessor.value.normalizeData(val)
    }
    
    // 立即发送数据更新事件，不等待质量检查
    emit('updateTradingViewData', {
      type: 'historical_data',
      data: processedData,
      pair: props.pair,
      resolution: props.resolution,
      timestamp: Date.now()
    })
    
  }, 'tradingview-klineList-watch')
})

// 监听ticker和klineTicker的组合变化（无缝衔接）
watch([ticker, klineTicker], ([val1, val2]) => {
  const currentPair = props.pair || pair.value
  const last = (val1[currentPair] || {}).last
  if (!last) return
  
  logger.debug('专业版无缝衔接数据更新', {
    pair: currentPair,
    price: last,
    currentPair: val2?.currentPair,
    currentPeriod: val2?.currentPeriod
  })
  
  // 构建标准化的K线数据
  const realtimeData: Partial<KlineBarData> = {
    close: Math.abs(Number(last)),
    high: val2?.high ? Math.abs(Number(val2.high)) : undefined,
    low: val2?.low ? Math.abs(Number(val2.low)) : undefined,
    open: val2?.open ? Math.abs(Number(val2.open)) : undefined,
    time: val2?.time ? Number(val2.time) : Date.now(),
    volume: val2?.volume ? Math.abs(Number(val2.volume)) : undefined
  }
  
  // 优化：简化无缝衔接逻辑，减少处理器干扰
  if (klineList.value && klineList.value.length > 0) {
    try {
      // 简化的时间边界检查
      const newTime = realtimeData.time!
      if (newTime > lastUpdateTime.value) {
        lastUpdateTime.value = newTime
        lastValidDataTime.value = newTime
        
        // 更新数据质量指标
        dataQualityMetrics.value.totalUpdates++
        dataQualityMetrics.value.validUpdates++
        dataQualityMetrics.value.seamlessConnections++
        dataQualityMetrics.value.lastUpdateTime = Date.now()
        
        // 优化：只在必要时进行复杂的无缝衔接处理
        const shouldProcessSeamless = dataQualityMetrics.value.totalUpdates % 5 === 0 || 
                                     Math.abs(realtimeData.close! - (klineList.value[klineList.value.length - 1]?.close || 0)) > 0.01
        
        if (shouldProcessSeamless) {
          // 执行完整的无缝衔接
          const mergedData = dataProcessor.value.seamlessConnect(
            klineList.value,
            realtimeData.close!,
            realtimeData.volume,
            realtimeData.time
          )
          
          if (mergedData.length > 0) {
            const lastBar = mergedData[mergedData.length - 1]
            
            emit('updateTradingViewData', {
              type: 'seamless_connection',
              data: lastBar,
              mergedData: mergedData,
              pair: currentPair,
              resolution: props.resolution,
              timestamp: newTime
            })
            
            logger.debug('专业版完整无缝衔接', {
              time: new Date(newTime).toISOString(),
              price: lastBar.close
            })
          }
        } else {
          // 简化的实时更新
          emit('updateTradingViewData', {
            type: 'realtime_update',
            data: {
              close: realtimeData.close!,
              high: realtimeData.high,
              low: realtimeData.low,
              open: realtimeData.open,
              volume: realtimeData.volume,
              time: newTime
            },
            pair: currentPair,
            resolution: props.resolution,
            timestamp: newTime
          })
          
          logger.debug('专业版简化实时更新', {
            time: new Date(newTime).toISOString(),
            price: realtimeData.close
          })
        }
      }
    } catch (error) {
      logger.warn('专业版实时数据处理失败，使用降级模式', error)
      dataQualityMetrics.value.invalidUpdates++
      
      // 降级处理：直接发送基础数据
      emit('updateTradingViewData', {
        type: 'fallback_update',
        data: {
          close: realtimeData.close!,
          time: realtimeData.time!
        },
        pair: currentPair,
        resolution: props.resolution,
        timestamp: Date.now()
      })
    }
  } else {
    // 没有历史数据时的处理
    const newTime = realtimeData.time!
    if (newTime > lastUpdateTime.value) {
      lastUpdateTime.value = newTime
      
      emit('updateTradingViewData', {
        type: 'initial_update',
        data: {
          close: realtimeData.close!,
          time: newTime
        },
        pair: currentPair,
        resolution: props.resolution,
        timestamp: newTime
      })
      
      logger.debug('专业版初始数据更新', {
        time: new Date(newTime).toISOString(),
        price: realtimeData.close
      })
    }
  }
}, {
  deep: true
})

// 组件销毁时清理资源
onBeforeUnmount(() => {
  logger.info('专业版组件卸载，清理资源')
  
  // 清除初始化相关定时器
  if (initializationTimer.value) {
    clearTimeout(initializationTimer.value)
    initializationTimer.value = null
  }
  
  if (subscriptionDelayTimer.value) {
    clearTimeout(subscriptionDelayTimer.value)
    subscriptionDelayTimer.value = null
  }
  
  // 清除延迟定时器
  if (resolutionChangeTimeout.value) {
    clearTimeout(resolutionChangeTimeout.value)
    resolutionChangeTimeout.value = null
  }
  
  // 清理widget
  if (widget) {
    try {
      widget.remove()
    } catch (error) {
      console.warn('[TradingView] Error removing widget:', error)
    }
    widget = null
  }
  
  // 取消所有活跃请求并清理datafeed缓存
  if (datafeedInstance) {
    try {
      if (typeof datafeedInstance.cancelAllActiveRequests === 'function') {
        datafeedInstance.cancelAllActiveRequests()
      }
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(false)
      }
    } catch (error) {
      console.warn('[TradingView] Error cleaning up datafeed:', error)
    }
  }
  
  // 重置状态
  isRebuilding.value = false
  Loading.value = true
  
  // 重置初始化状态
  initializationState.value.phase = 'waiting'
  initializationState.value.websocketReady = false
  initializationState.value.widgetReady = false
  initializationState.value.dataSubscribed = false
  
  logger.info('专业版资源清理完成')
})
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>