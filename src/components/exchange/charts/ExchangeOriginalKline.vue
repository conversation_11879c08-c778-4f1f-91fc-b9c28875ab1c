<template>
  <div class="original-kline-wrap">
    <div
      v-if="isShowTool"
      class="line-tool"
      >
      <el-scrollbar
        class="line-tool-scroll-wrap"
        view-class="flex-box flex-column"
        tag="ul"
        >
        <el-tooltip
          v-for="item in drawlinedata"
          :key="item.label"
          :close-delay="0"
          :disabled="item.line"
          placement="right"
          :content="item.label"
          >
          <li
            v-if="item.line"
            class="tool-split-line"
          ></li>
          <template v-else>
            <li
              v-if="!item.line"
              :class="[
                {
                  'active': item.value === '' ? (!drawLine || drawLine === '') : (drawLine === item.value || item.active)
                }
              ]"
              class="line-tool-item mg-t4 ts-24"
              @click="drawlineChange(item.value)"
            >
              <MonoCross
                size="24"
                v-if="item.icon === 'icon-my-cross'"
              />
              <MonoFibonacci
                size="24"
                v-else-if="item.icon === 'icon-FibonacciRetracements'"
              />
              <MonoHorizonta
                size="24"
                v-else-if="item.icon === 'icon-my-horizontal-extended'"
              />
              <MonoParallel
                size="24"
                v-else-if="item.icon === 'icon-my-parallel-lines'"
              />
              <MonoPriceLine
                size="24"
                v-else-if="item.icon === 'icon-my-rice-line'"
              />
              <MonoRay
                size="24"
                v-else-if="item.icon === 'icon-my-ray'"
              />
              <MonoVerticalLine
                size="24"
                v-else-if="item.icon === 'icon-my-vertical-line'"
              />
              <MonoDelete
                size="24"
                v-else-if="item.icon === 'icon-my-delete'"
              />
            </li>
          </template>
        </el-tooltip>
      </el-scrollbar>
    </div>
    <div
      id="chart"
      class="charts-origin-box"
      :class="isShowTool ? 'offset' : ''"
    />
    <div
      v-if="isLoading"
      v-loading="isLoading"
      class="loadingBox"
    >
    </div>
  </div>
  <TechniCalInDicator
    v-if="isShowSetting"
    :originalTechnicalIndicatorSettings="originalTechnicalIndicatorSettings"
    :isShow="isShowSetting"
    @close="emit('closeSetting')"
    @updateOriginalTechnicalIndicatorSettings="updateSetting"
  />
</template>
<script lang="ts" setup>
import { ElScrollbar, ElTooltip } from 'element-plus'
import { init, dispose, registerLocale } from 'klinecharts'
import useOriginal, { baseTechnicalIndicatorMap } from '~/composables/useOriginal'
import MonoCross from '~/components/common/icon-svg/MonoCross.vue'
import MonoFibonacci from '~/components/common/icon-svg/MonoFibonacci.vue'
import MonoHorizonta from '~/components/common/icon-svg/MonoHorizonta.vue'
import MonoParallel from '~/components/common/icon-svg/MonoParallel.vue'
import MonoPriceLine from '~/components/common/icon-svg/MonoPriceLine.vue'
import MonoRay from '~/components/common/icon-svg/MonoRay.vue'
import MonoVerticalLine from '~/components/common/icon-svg/MonoVerticalLine.vue'
import TechniCalInDicator from './TechniCalInDicator.vue'
import { commonStore } from '~/stores/commonStore'
import { getKlinesApi } from '~/api/order'
import { nextTick } from 'vue'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const store = commonStore()
const { klineList, klineTicker, ticker, pairInfo, isPairDetail } = storeToRefs(store)
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  isShowOriginalSetting: {
    type: Boolean,
    default: false
  },
  isShowDrawLine: {
    type: Boolean,
    default: true
  },
  resolution: {
    type: String,
    default: '15m'
  }
})
const isLoading = ref(false)
const emit = defineEmits(['closeSetting'])
let chart: any
const isShowTool = ref(false)
const isShowSetting = ref(false)
const drawlinedata = computed(() => {
  return [
    { label: t('十字线'), value: '', icon: 'icon-my-cross' },
    { line: true },
    { label: t('水平线'), value: 'horizontalStraightLine', icon: 'icon-my-horizontal-extended' },
    { label: t('垂直线'), value: 'verticalStraightLine', icon: 'icon-my-vertical-line' },
    { label: t('射线'), value: 'rayLine', icon: 'icon-my-ray' },
    // { label: t('平行射线'), value: 'horizontalRayLine', icon: 'icon-my-parallel-rays' },
    { label: t('平行直线'), value: 'parallelStraightLine', icon: 'icon-my-parallel-lines' },
    // { label: t('箭头'), value: 'JT', icon: 'icon-my-arrow' },
    { label: t('价格通道线'), value: 'priceLine', icon: 'icon-my-rice-line' },
    { label: t('斐波那契回调直线'), value: 'fibonacciLine', icon: 'icon-FibonacciRetracements' },
    { line: true },
    // { label: t('测量工具'), value: 'CL', icon: 'icon-Tools' },
    // { line: true },
    // { label: t('隐藏已画线'), value: 'yc', icon: 'icon-my-cover', active: !this.isShowLine },
    { label: t('清除画线'), value: 'del', icon: 'icon-my-delete' }
  ]
})
const drawLine = ref('')
watch(() => props.isShowOriginalSetting, (settingVal) => {
  console.info(settingVal, 'settingValsettingValsettingVal')
  isShowSetting.value = settingVal
})
watch(() => isShowTool.value, (val) => {
  nextTick(() => {
    chart && chart.resize()
  })
})
watch(() => props.isShowDrawLine, (val) => {
  isShowTool.value = val
}, { immediate: true })
watch(() => colorMode.preference, (val) => {
  chart.setStyles(val)
  if (val === 'light') {
    document.getElementById('chart').style.backgroundColor = '#ffffff'
  } else if (val === 'dark') {
    document.getElementById('chart').style.backgroundColor = '#1b1b1f'
  }
})

watch(() => locale.value, (lang) => {
  chart.setLocale(lang)
})
const drawlineChange = (shapeName: string) => {
  drawLine.value = shapeName
  if (shapeName === 'del') {
    chart.removeOverlay()
    drawLine.value = ''
  } else {
    chart.createOverlay({
      name: shapeName,
      points: [],
      styles: {
        text: {
          offset: [-2, 0]
        },
        line: {
          size: 2
        }
      },
      onRightClick() {
        return true
      },
      onDrawEnd({ overlay: { points } }: any) {
        drawLine.value = ''
      }
    })
  }
}
const originalTechnicalIndicatorSettings = ref({
  checkedIndicators: { // 基础版k线当前选中的技术指标
    main: ['MA'],
    sub: ['VOL']
  },
  technicalIndicatorSettings: baseTechnicalIndicatorMap
})
// 设置主指标参数和样式
const mainSettingFunc = () => {
  // 主指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    console.info(settings[name], 'settingssettingssettings')
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: [],
      circles: []
    }
    const basicSettings = settings[name].setting

    if (['MA', 'EMA', 'SMMA'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'BOLL') {
      calcParams = [basicSettings.cycle.value, basicSettings.deviation.value]
      styles.lines = [{
        size: 1,
        color: basicSettings.BOLL.color
      }, {
        size: 1,
        color: basicSettings.UB.color
      }, {
        size: 1,
        color: basicSettings.DB.color
      }]
    } else if (name === 'SAR') {
      calcParams = [
        basicSettings.start.value,
        basicSettings.step.value,
        basicSettings.max.value
      ]
      styles.circles = [{
        upColor: basicSettings.SAR.color,
        downColor: basicSettings.SAR.color,
        noChangeColor: basicSettings.SAR.color
      }]
    }
    console.info({
      name,
      calcParams,
      styles
    }, 'overrideIndicatoroverrideIndicator')
    chart.overrideIndicator({
      name,
      visible: true,
      calcParams,
      styles
    })
  })
}
// 设置副指标参数和样式
const subSettingFunc = () => {
  // 副指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: []
    }
    const basicSettings = settings[name].setting
    if (['VOL', 'RSI'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'MACD') {
      calcParams = [basicSettings.MACD1.value, basicSettings.MACD2.value, basicSettings.MACD3.value]
      styles.line.colors = [basicSettings.DIF.color, basicSettings.DEA.color]
    } else if (name === 'KDJ') {
      calcParams = [basicSettings.KDJ1.value, basicSettings.KDJ2.value, basicSettings.KDJ3.value]
      styles.line.colors = [basicSettings.K.color, basicSettings.D.color, basicSettings.J.color]
    } else if (name === 'OBV') {
      calcParams = [basicSettings.MAOBV.value]
      styles.line.colors = [basicSettings.OBV.color, basicSettings.MAOBV.color]
    } else if (name === 'CCI') {
      calcParams = [basicSettings.CCI.value]
      styles.line.colors = [basicSettings.CCI.color]
    } else if (name === 'WR') {
      calcParams = [basicSettings.WR.value]
      styles.line.colors = [basicSettings.WR.color]
    } else if (name === 'DMI') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.PDI.color, basicSettings.MDI.color, basicSettings.ADX.color, basicSettings.ADXR.color]
    } else if (name === 'MTM') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.MTM.color, basicSettings.MAMTM.color]
    } else if (name === 'EMV') {
      calcParams = [basicSettings.EMV1.value, basicSettings.EMV2.value]
      styles.line.colors = [basicSettings.EMV.color, basicSettings.MAEMV.color]
    }
    chart.overrideIndicator({
      name,
      calcParams,
      styles
    })
  })
}
const paneMap: any = {}
const initTechnicalIndicator = () => {
  const nowPane: any = []
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true, { id: 'candle_pane' })
      paneMap[name] = paneId
    }
  })
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true)
      paneMap[name] = paneId
    }
  })
  Object.keys(paneMap).forEach(name => {
    if (!nowPane.includes(name)) {
      chart.removeIndicator(paneMap[name], name)
      delete paneMap[name]
    }
  })
}
// watch(originalTechnicalIndicatorSettings.value, (val) => {
//   console.info(val, 'watchwatchwatchwatch')
//   if (chart) {
//     initTechnicalIndicator()
//     mainSettingFunc()
//     subSettingFunc()
//   }
// })
const isReady = ref(false)
const updateChart = val => {
  if (val.length > 0 && JSON.stringify(pairInfo.value[props.pair]) !== '{}') {
    isLoading.value = false
    // console.log(998899, val.length, pairInfo.value[props.pair], chart)
    chart && chart.setPrecision({
      price: (pairInfo.value[props.pair] || {}).price_scale,
      volume: 2
    })
  }
  if (val.length > 0) {
    const timer = setTimeout(() => {
      chart && chart.updateData(val[val.length - 1])
      clearTimeout(timer)
    }, 0)
  }
}

watch(klineList, (val) => {
  console.log('基本版K线数据更新:', val.length, '条数据')

  // 根据分辨率类型决定是否启用loadMore
  const isLongTermResolution = props.resolution === '1w' || props.resolution === '1M'
  const isWebSocketResolution = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'].includes(props.resolution)

  let enableLoadMore = false
  if (isLongTermResolution) {
    // 1w和1M可以启用loadMore，但要谨慎
    enableLoadMore = val.length >= 50
  } else if (isWebSocketResolution) {
    // 1d及短周期分辨率不应该启用loadMore
    enableLoadMore = false
    console.log(`${props.resolution}是WebSocket分辨率，watch中禁用loadMore`)
  } else {
    // 其他分辨率保持原有逻辑
    enableLoadMore = val.length >= 200
  }

  console.log(`klineList watch: ${props.resolution}, 数据量: ${val.length}, 启用loadMore: ${enableLoadMore}`)
  chart && chart.applyNewData(val, enableLoadMore)
  updateChart(val)
})
watch([ticker, klineTicker], ([val1,val2]) => {
  const last = (val1[props.pair] || {}).last
  console.log(last, '')
  if (last) {
    const resultVal = {
      close: Number(last) < 0 ? -Number(last) : Number(last),
      currentPair: val2.currentPair,
      currentPeriod: val2.currentPeriod,
      high: Number(val2.high) < 0 ? -Number(val2.high) : Number(val2.high),
      low: Number(val2.low) < 0 ? -Number(val2.low) : Number(val2.low),
      open: Number(val2.open) < 0 ? -Number(val2.open) : Number(val2.open),
      time: Number(val2.time),
      timestamp: Number(val2.timestamp),
      undefined: Number(val2.undefined),
      volume: Number(val2.volume) < 0 ? -Number(val2.volume) : Number(val2.volume)
    }
    chart && chart.updateData(resultVal)
  }
}, {
  deep: true
})
const updateSetting = (data) => {
  console.log(data, 'dhddujeijidieieiie')
  originalTechnicalIndicatorSettings.value = data
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
}
const transChartData = (obj) => {
  const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
  const finalItem = {
    time: Number(obj[0])
  }
  obj.forEach((v, i) => {
    finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
  })
  console.log('Transformed data:', finalItem);
  return finalItem
}
// 基本版请求去重机制
const basicVersionRequestCache = new Map()

const request = async (end?: number, callback?: any, retryCount = 3) => {
  try {
    // 分辨率分类
    const isLongTermResolution = props.resolution === '1w' || props.resolution === '1M'
    const isWebSocketResolution = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'].includes(props.resolution)
    const requestKey = `${props.pair}-${props.resolution}-${end || 'initial'}`

    // 长期分辨率的请求去重
    if (isLongTermResolution && basicVersionRequestCache.has(requestKey)) {
      const cachedResult = basicVersionRequestCache.get(requestKey)
      console.log(`基本版${props.resolution}使用缓存数据，避免重复请求`)
      return cachedResult
    }

    // WebSocket分辨率的警告（1d不应该频繁调用API）
    if (isWebSocketResolution && !end) {
      console.warn(`⚠️ ${props.resolution}是WebSocket分辨率，不应该通过API获取初始数据，建议使用WebSocket`)
    }

    // 根据分辨率调整请求参数
    let requestLimit = 300 // 默认值
    if (isLongTermResolution) {
      requestLimit = 100 // 长期分辨率使用更小的limit
    } else if (isWebSocketResolution) {
      requestLimit = 200 // WebSocket分辨率适中的limit，主要用于loadMore
    }

    console.log(`基本版请求API: ${props.resolution}, limit: ${requestLimit}, before: ${end}, 类型: ${isLongTermResolution ? '长期' : isWebSocketResolution ? 'WebSocket' : '其他'}`)

    const { data } = await getKlinesApi({
      symbol: props.pair,
      market: props.pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: props.resolution,
      before: end,
      limit: requestLimit
    });

    if (data?.e) {
      const klineData = data.e.map(item => transChartData(item));

      // 长期分辨率缓存结果，避免重复请求
      if (isLongTermResolution) {
        basicVersionRequestCache.set(requestKey, klineData)
        // 5分钟后清理缓存
        setTimeout(() => {
          basicVersionRequestCache.delete(requestKey)
        }, 5 * 60 * 1000)
      }

      console.log(`基本版API返回数据: ${props.resolution}, 数量: ${klineData.length}`)
      return klineData;
    } else {
      console.log('No data received from API');
      return [];
    }
  } catch (error) {
    if (error.response?.status === 504 && retryCount > 0) {
      // 如果是504错误且还有重试次数，等待一段时间后重试
      const delay = (4 - retryCount) * 1000; // 重试延迟时间递增：1s, 2s, 3s
      console.warn(`504 Gateway Timeout, retrying in ${delay/1000}s... (${retryCount} retries left)`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return request(end, callback, retryCount - 1);
    }

    console.error('API request failed:', error);
    return [];
  }
}
const chartInit = () => {
  const subscriptNumbers = {
    0: '₀',
    1: '₁',
    2: '₂',
    3: '₃',
    4: '₄',
    5: '₅',
    6: '₆',
    7: '₇',
    8: '₈',
    9: '₉',
    10: '₁₀',
    11: '₁₁',
    12: '₁₂',
    13: '₁₃',
    14: '₁₄',
    15: '₁₅',
    16: '₁₆',
    17: '₁₇',
    18: '₁₈',
    19: '₁₉',
    20: '₂₀',
    21: '₂₁',
    22: '₂₂',
    23: '₂₃',
    24: '₂₄',
    25: '₂₅',
    26: '₂₆',
    27: '₂₇',
    28: '₂₈',
    29: '₂₉',
    30: '₃₀',
    31: '₂₁',
    32: '₃₂',
    33: '₃₃'
  }
  chart = init('chart', {
    thousandsSeparator: '',
    decimalFoldThreshold: 8,
    loadMore: {
      isLoadMore: true, // 启用加载更多
    }
  }) as any
  chart.setLoadMoreDataCallback(async ({ type, data, callback }) => {
    if (type === 'forward') {
      // 分辨率类型检查
      const isLongTermResolution = props.resolution === '1w' || props.resolution === '1M'
      const isWebSocketResolution = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'].includes(props.resolution)

      // WebSocket分辨率严格限制loadMore
      if (isWebSocketResolution) {
        console.warn(`⚠️ ${props.resolution}是WebSocket分辨率，不应该触发loadMore，直接返回空数据`)
        callback([], false) // 直接返回，不调用API
        return
      }

      const end = data ? data.timestamp : Date.now();
      console.log(`基本版loadMore触发: ${props.resolution}, 时间戳: ${end}`)

      const klineData = await request(end);
      let hasMoreData = false

      if (isLongTermResolution) {
        // 1w和1M：只有当返回数据量达到请求量时才认为还有更多数据
        hasMoreData = klineData.length >= 80 // 进一步降低阈值
      } else {
        // 其他分辨率：保持原有逻辑
        hasMoreData = klineData.length >= 200
      }

      console.log(`基本版loadMore: ${props.resolution}, 数据量: ${klineData.length}, 还有更多: ${hasMoreData}`)
      callback(klineData, hasMoreData);
    } else {
      callback([], false);
    }
  });
  // 根据分辨率类型决定是否启用loadMore
  const isLongTermResolution = props.resolution === '1w' || props.resolution === '1M'
  const isWebSocketResolution = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'].includes(props.resolution)

  let enableLoadMore = false
  if (isLongTermResolution) {
    // 1w和1M可以启用loadMore，但要谨慎
    enableLoadMore = klineList.value.length >= 50
  } else if (isWebSocketResolution) {
    // 1d及短周期分辨率不应该启用loadMore
    enableLoadMore = false
    console.log(`${props.resolution}是WebSocket分辨率，禁用loadMore`)
  } else {
    // 其他分辨率保持原有逻辑
    enableLoadMore = klineList.value.length === 300
  }

  console.log(`chartInit: ${props.resolution}, 数据量: ${klineList.value.length}, 启用loadMore: ${enableLoadMore}`)
  chart && chart.applyNewData(klineList.value, enableLoadMore)
  chart.setDecimalFold({
    format: value => {
      let vl = `${value}`;
      const [integer, decimalPart] = vl.split('.');
      const trimmedDecimalPart = decimalPart ? decimalPart.replace(/0+$/, '') : '';
      vl = integer + (trimmedDecimalPart ? '.' + trimmedDecimalPart : '');
      const reg = new RegExp('\\.0{8,}[1-9][0-9]*$');
      if (reg.test(vl)) {
        const result = vl.split('.');
        const lastIndex = result.length - 1;
        const v = result[lastIndex];
        const match = /0*/.exec(v);
        if (match) {
          const count = match[0].length;
          result[lastIndex] = v.replace(/0*/, `0${subscriptNumbers[count]}`);
          return result.join('.')
        }
      }
      return vl;
    }
  })
  chart.setStyles(useOriginal(colorMode.preference, 'green-up'))
  chart.setLocale(locale.value)
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
  registerLocale('zh', {
    time: '时间：',
    open: '开：',
    high: '高：',
    low: '低：',
    close: '收：',
    volume: '成交量：'
  })
  registerLocale('en', {
    time: 'Time：',
    open: 'Open：',
    high: 'High：',
    low: 'Low：',
    close: 'Close：',
    volume: 'Volume：'
  })
  registerLocale('ja', {
    time: '時間：',
    open: '始値：',
    high: '高値：',
    low: '安値：',
    close: '終値：',
    volume: '出来高：'
  })
  registerLocale('ko', {
    time: '시간：',
    open: '시가：',
    high: '고가：',
    low: '저가：',
    close: '종가：',
    volume: '거래량：'
  })
  registerLocale('zh-Hant', {
    time: '時間：',
    open: '開盤：',
    high: '最高：',
    low: '最低：',
    close: '收盤：',
    volume: '成交量：'
  })
  isReady.value = true
}
onMounted(() => {
  console.log('基本版K线组件挂载，当前klineList长度:', klineList.value.length)
  isLoading.value = true
  window.addEventListener(
    'resize',
    () => {
      setTimeout(() => {
        chart && chart.resize()
      }, 300)
    }
  )
  chartInit()

  // 重新定义分辨率分类策略
  const isLongTermResolution = props.resolution === '1w' || props.resolution === '1M' // 长期分辨率：数据量少，适合API
  const isWebSocketResolution = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'].includes(props.resolution) // WebSocket分辨率：实时性要求高

  if (klineList.value.length === 0) {
    console.log('基本版组件挂载时klineList为空，需要加载数据')

    if (isLongTermResolution) {
      // 1w和1M优先使用API机制获取数据，避免与WebSocket冲突
      console.log(`${props.resolution}使用API机制获取初始数据（长期分辨率）`)
      nextTick(async () => {
        try {
          const initialData = await request()
          if (initialData && initialData.length > 0) {
            // 直接更新klineList，让watch处理图表更新
            store.klineList = initialData
            console.log(`${props.resolution}初始数据加载完成:`, initialData.length)
          }
        } catch (error) {
          console.error(`${props.resolution}初始数据加载失败:`, error)
          // 降级到WebSocket方式
          store.getKlineSocket(props.pair, props.resolution)
        }
      })
    } else if (isWebSocketResolution) {
      // 1d及短周期分辨率使用WebSocket，包括实时推送
      console.log(`${props.resolution}使用WebSocket获取数据（支持实时推送）`)
      nextTick(() => {
        store.getKlineSocket(props.pair, props.resolution)
      })
    } else {
      // 其他未分类的分辨率降级到WebSocket
      console.log(`${props.resolution}使用WebSocket获取数据（默认策略）`)
      nextTick(() => {
        store.getKlineSocket(props.pair, props.resolution)
      })
    }
  }
})
onUnmounted(() => {
  // 清理基本版的请求缓存
  basicVersionRequestCache.clear()
  console.log('基本版组件卸载，清理请求缓存')

  // 清理图表实例
  if (chart) {
    dispose('chart')
    chart = null
  }
})
</script>
<style lang="scss" scoped>
.original-kline-wrap {
  width: 100%;
  height: calc(100% - 42px);
  position: relative;
  overflow: hidden; // 添加这行
  .line-tool {
    position: relative;
    height: 100%;
    width: 52px;
    z-index: 1;

    .line-tool-scroll-wrap {
      position: relative;
      height: 100%;
      width: 52px;
    }

    .line-tool-item {
      width: 34px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      @include color(tc-secondary);

      &:hover {
        @include color(theme);
        @include bg-color(bg-quaternary);
      }

      &.active {
        @include color(theme);
      }

      &:not(:first-of-type) {
        margin-top: 4px;
      }
    }

    .tool-split-line {
      height: 1px;
      width: 100%;
      margin: 4px 0;
      @include bg-color(border);
    }
  }

  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
}

.charts-origin-box {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  transform: translateZ(0); // 添加硬件加速
  &.offset {
    left: 52px;
    width: calc(100% - 52px);
  }
}
@include mb {
  .original-kline-wrap {
    width: 100%;
    height: calc(100% - 44px);
  }
}
</style>