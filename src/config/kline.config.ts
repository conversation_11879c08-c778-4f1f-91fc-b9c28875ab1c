/**
 * K线相关配置文件
 * 包含日志配置、数据处理配置等
 */

// 日志级别
type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'silent'

// 日志配置
interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableTimestamp: boolean
  maxLogEntries: number
}

// K线日志器实现
export class KlineLogger {
  private static instance: KlineLogger
  private config: LoggerConfig
  private logs: Array<{ level: LogLevel; message: string; data?: any; timestamp: number }> = []

  private constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableTimestamp: true,
      maxLogEntries: 1000,
      ...config
    }
  }

  static getInstance(config?: Partial<LoggerConfig>): KlineLogger {
    if (!KlineLogger.instance) {
      KlineLogger.instance = new KlineLogger(config)
    }
    return KlineLogger.instance
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'silent']
    const currentLevelIndex = levels.indexOf(this.config.level)
    const messageLevelIndex = levels.indexOf(level)
    return messageLevelIndex >= currentLevelIndex
  }

  private log(level: LogLevel, message: string, data?: any) {
    if (!this.shouldLog(level) || level === 'silent') return

    const timestamp = Date.now()
    const logEntry = { level, message, data, timestamp }

    // 添加到日志缓存
    this.logs.push(logEntry)
    if (this.logs.length > this.config.maxLogEntries) {
      this.logs = this.logs.slice(-this.config.maxLogEntries / 2)
    }

    // 控制台输出
    if (this.config.enableConsole && typeof console !== 'undefined') {
      const prefix = this.config.enableTimestamp 
        ? `[${new Date(timestamp).toISOString()}] [${level.toUpperCase()}]`
        : `[${level.toUpperCase()}]`
      
      const logMessage = `${prefix} ${message}`
      
      switch (level) {
        case 'debug':
          console.debug(logMessage, data || '')
          break
        case 'info':
          console.info(logMessage, data || '')
          break
        case 'warn':
          console.warn(logMessage, data || '')
          break
        case 'error':
          console.error(logMessage, data || '')
          break
      }
    }
  }

  debug(message: string, data?: any) {
    this.log('debug', message, data)
  }

  info(message: string, data?: any) {
    this.log('info', message, data)
  }

  warn(message: string, data?: any) {
    this.log('warn', message, data)
  }

  error(message: string, data?: any) {
    this.log('error', message, data)
  }

  // 获取日志历史
  getLogs(level?: LogLevel, limit?: number): typeof this.logs {
    let filteredLogs = level 
      ? this.logs.filter(log => log.level === level)
      : this.logs

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit)
    }

    return filteredLogs
  }

  // 清空日志
  clearLogs() {
    this.logs = []
  }

  // 更新配置
  updateConfig(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config }
  }
}

// K线数据处理配置
export const KLINE_CONFIG = {
  // 时间间隔映射（毫秒）
  INTERVALS: {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '30m': 30 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '2h': 2 * 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '8h': 8 * 60 * 60 * 1000,
    '12h': 12 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
    '3d': 3 * 24 * 60 * 60 * 1000,
    '1w': 7 * 24 * 60 * 60 * 1000,
    '1M': 30 * 24 * 60 * 60 * 1000
  },

  // 数据验证配置
  VALIDATION: {
    MIN_PRICE: 0,
    MAX_PRICE: Number.MAX_SAFE_INTEGER,
    MIN_VOLUME: 0,
    MAX_VOLUME: Number.MAX_SAFE_INTEGER,
    REQUIRED_FIELDS: ['time', 'open', 'high', 'low', 'close', 'volume']
  },

  // 性能配置
  PERFORMANCE: {
    MAX_DATA_POINTS: 5000,
    CACHE_SIZE_LIMIT: 100 * 1024 * 1024, // 100MB
    PROCESSING_TIMEOUT: 10000, // 10s
    BATCH_SIZE: 1000
  },

  // 数据质量配置
  DATA_QUALITY: {
    MAX_GAP_TOLERANCE: 3, // 最大允许的数据间隙数量
    PRICE_CHANGE_THRESHOLD: 0.5, // 50% 价格变化阈值
    VOLUME_SPIKE_THRESHOLD: 10, // 10倍成交量异常阈值
    TIME_DRIFT_TOLERANCE: 30000 // 30秒时间漂移容忍度
  }
} as const

// 导出类型
export type KlineInterval = keyof typeof KLINE_CONFIG.INTERVALS
export type { LogLevel, LoggerConfig }

// 默认导出配置
export default KLINE_CONFIG