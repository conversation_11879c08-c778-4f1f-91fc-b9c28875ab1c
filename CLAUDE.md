# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Nuxt.js 3 的加密货币交易所前端应用，采用 SSR (Server-Side Rendering) 架构，项目名为 "madex-ssr"。主要提供现货交易、期货交易、用户管理、资产管理等功能。

## 常用开发命令

### 开发环境
- `npm run dev` - 本地开发环境 (使用 .env.local)
- `npm run test` - 测试环境 (使用 .env.development) 
- `npm run pro` - 生产环境开发调试 (使用 .env.production)

### 构建部署
- `npm run build` - 生产环境构建 (使用 .env.production)
- `npm run build:test` - 测试环境构建 (使用 .env.development)
- `npm run build:local` - 本地环境构建 (使用 .env.local)
- `npm run start` - 启动构建后的应用
- `npm run static` - 上传静态资源到OSS

### 其他命令
- `npm run generate` - 生成静态站点
- `npm run preview` - 预览构建结果
- `pm2 start ecosystem.config.js --env production` - PM2生产环境启动

## 技术栈与架构

### 核心框架
- **Nuxt.js 3** - Vue.js全栈框架，当前配置为SPA模式 (ssr:false)
- **Vue 3** + **TypeScript** - 组件开发
- **Pinia** - 状态管理，支持持久化
- **Vue I18n** - 国际化支持 (en, zh, ja, ko, zh-Hant)

### 主要依赖
- **Element Plus** - UI组件库
- **Vant** - 移动端UI组件
- **TradingView** - 图表组件 (K线图)
- **Echarts** - 数据可视化
- **KlineCharts** - K线图表库
- **Axios** - HTTP客户端

### 项目结构
```
src/
├── api/              # API接口定义
├── assets/           # 静态资源 (样式、图片、字体)
├── components/       # Vue组件
│   ├── common/       # 通用组件
│   ├── exchange/     # 现货交易相关
│   ├── future/       # 期货交易相关
│   ├── header/       # 头部组件
│   └── my/           # 用户中心相关
├── composables/      # 组合式函数
├── config/           # 配置文件
├── i18n/             # 国际化配置
├── layouts/          # 布局组件
├── locales/          # 多语言资源
├── pages/            # 路由页面
├── plugins/          # Nuxt插件
├── stores/           # Pinia状态管理
└── utils/            # 工具函数
```

### 核心业务模块
1. **交易系统** - 现货/期货交易，K线图，订单管理
2. **用户系统** - 登录注册，KYC认证，资产管理
3. **市场数据** - 实时行情，WebSocket连接
4. **国际化** - 多语言支持，主题切换

## 开发规范

### 组件命名
- 使用PascalCase命名组件文件
- 页面组件使用kebab-case路由
- 通用组件放在 `components/common/`

### 状态管理
- 使用Pinia进行状态管理
- 状态自动持久化到localStorage
- 主要Store: userStore, commonStore, marketsStore, futureStore

### API接口
- 接口定义在 `src/api/` 目录
- 使用统一的HTTP客户端配置
- 测试环境: `https://api.ktx.one`
- 生产环境: `https://api.ktx.com`

### 样式开发
- 使用SCSS预处理器
- 全局样式在 `assets/style/` 目录
- 支持深色/浅色主题切换
- 响应式设计，支持移动端

### 部署配置
- 使用PM2进行进程管理
- 支持集群模式 (4个实例)
- CDN静态资源托管: `https://res.ktx.com/`
- 构建产物分包优化: vue-core, element-ui, tradingview等

## 重要配置
- 端口: 3009
- 国际化策略: prefix
- 默认语言: en
- WebSocket: `wss://stream-market.tonetou.com`
- 项目标识: madex-pro